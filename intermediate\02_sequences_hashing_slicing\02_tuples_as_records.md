# 2.2 Tuples as Records (元组作为记录)

Tuples are not just immutable lists. They can also be used as records with no field names. The `collections.namedtuple` function is a factory that produces subclasses of `tuple` enhanced with field names and a class name.

元组不仅仅是不可变的列表。它们也可以用作没有字段名的记录。`collections.namedtuple` 函数是一个工厂，它生成 `tuple` 的子类，并增强了字段名和类名。

## `collections.namedtuple`

`namedtuple` allows you to create simple classes for grouping data. It's a memory-efficient way to have a class that is basically a tuple, but with named fields.

`namedtuple` 允许你创建用于分组数据的简单类。这是一种内存效率高的方式，可以拥有一个基本上是元组但带有命名字段的类。

```python
# code/namedtuple_examples.py

from collections import namedtuple

# Create a named tuple class (创建一个命名元组类)
City = namedtuple('City', 'name country population coordinates')

# Create an instance of the class (创建类的实例)
tokyo = City('Tokyo', 'JP', 36.933, (35.689722, 139.691667))

# Access fields by name (按名称访问字段)
print(f"City: {tokyo.name}")
print(f"Country: {tokyo.country}")

# Access fields by index (按索引访问字段)
print(f"Population: {tokyo[2]}")
```

## Unpacking Tuples (解包元组)

One of the most powerful features of tuples is unpacking. You can unpack a tuple into separate variables.

元组最强大的功能之一是解包。你可以将一个元组解包到单独的变量中。

```python
# code/unpacking_examples.py

lax_coordinates = (33.9425, -118.408056)
latitude, longitude = lax_coordinates  # unpacking

print(f"Latitude: {latitude}")
print(f"Longitude: {longitude}")
```
