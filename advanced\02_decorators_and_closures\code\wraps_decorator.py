# code/wraps_decorator.py

import functools

def trace(func):
    @functools.wraps(func)
    def wrapper(*args, **kwargs):
        """This is the wrapper function."""
        print(f"Calling {func.__name__} with args={args}, kwargs={kwargs}")
        result = func(*args, **kwargs)
        print(f"{func.__name__} returned {result}")
        return result
    return wrapper

@trace
def add(a, b):
    """This function adds two numbers."""
    return a + b

print(f"add.__name__: {add.__name__}")
print(f"add.__doc__: {add.__doc__}")
add(2, 3)
