# 2.3 Slicing (切片)

A common feature of `list`, `tuple`, `str`, and all sequence types in Python is the support for slicing.

在Python中，`list`、`tuple`、`str` 和所有序列类型的一个共同特性是支持切片。

## Why Slices and Range Exclude the Last Item (为什么切片和范围不包括最后一项)

The Pythonic way of slicing and ranging excludes the last item because it makes it easier to work with zero-based indexes and to split sequences into non-overlapping parts.

Pythonic的切片和范围方式排除了最后一项，因为这使得使用从零开始的索引和将序列分割成不重叠的部分变得更加容易。

```python
# code/slicing_examples.py

my_list = [0, 1, 2, 3, 4, 5]

# The length of my_list[:n] is n
# (my_list[:n]的长度是n)
print(f"my_list[:3] -> {my_list[:3]}, length is {len(my_list[:3])}")

# The length of my_list[n:] is len(my_list) - n
# (my_list[n:]的长度是len(my_list) - n)
print(f"my_list[3:] -> {my_list[3:]}, length is {len(my_list[3:])}")
```

## Slice Objects (切片对象)

You can also create `slice` objects and use them in the `[]` operator.

你也可以创建 `slice` 对象，并在 `[]` 运算符中使用它们。

```python
# code/slice_object_examples.py

invoice = """
0.....6.................................40........52...55........
1909 Pimoroni PiBrella                      $17.50    3    $52.50
1489 6mm Tactile Switch x20                  $4.95    2     $9.90
"""

SKU = slice(0, 6)
DESCRIPTION = slice(6, 40)
UNIT_PRICE = slice(40, 52)

line_items = invoice.split('\n')[2:]

for item in line_items:
    print(item[SKU], item[DESCRIPTION], item[UNIT_PRICE])
```

## Assigning to Slices (给切片赋值)

You can also assign to slices, which is a powerful way to modify a sequence.

你也可以给切片赋值，这是一种修改序列的强大方法。

```python
# code/assign_to_slice_examples.py

my_list = list(range(10))
print(f"Original list: {my_list}")

my_list[2:5] = [20, 30]
print(f"After my_list[2:5] = [20, 30]: {my_list}")

del my_list[5:7]
print(f"After del my_list[5:7]: {my_list}")
```
