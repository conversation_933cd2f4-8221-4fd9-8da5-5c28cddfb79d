# Chapter 9: Asynchronous Programming (异步编程)

异步编程是现代Python开发中的重要技术，特别适用于I/O密集型应用。Python的asyncio库提供了强大的异步编程支持，让我们能够编写高效的并发代码。

Asynchronous programming is an important technique in modern Python development, particularly suitable for I/O-intensive applications. Python's asyncio library provides powerful asynchronous programming support, allowing us to write efficient concurrent code.

## Table of Contents (目录)

1. [Introduction to Async Programming (异步编程简介)](01_introduction.md)
2. [Asyncio Fundamentals (asyncio基础)](02_asyncio_fundamentals.md)
3. [Coroutines and Tasks (协程与任务)](03_coroutines_tasks.md)
4. [Event Loop (事件循环)](04_event_loop.md)
5. [Async Context Managers (异步上下文管理器)](05_async_context_managers.md)
6. [Async Iterators and Generators (异步迭代器与生成器)](06_async_iterators_generators.md)
7. [Concurrency Patterns (并发模式)](07_concurrency_patterns.md)
8. [Error Handling in Async Code (异步代码错误处理)](08_error_handling.md)
9. [Performance and Best Practices (性能与最佳实践)](09_performance_best_practices.md)

## Learning Objectives (学习目标)

完成本章学习后，你将能够：

After completing this chapter, you will be able to:

- 理解异步编程的概念和优势
- 掌握asyncio库的核心功能
- 编写和管理协程和任务
- 理解和操作事件循环
- 创建异步上下文管理器和迭代器
- 应用各种并发模式
- 处理异步代码中的错误
- 优化异步程序的性能

- Understand the concepts and advantages of asynchronous programming
- Master the core functionality of the asyncio library
- Write and manage coroutines and tasks
- Understand and manipulate event loops
- Create async context managers and iterators
- Apply various concurrency patterns
- Handle errors in asynchronous code
- Optimize the performance of asynchronous programs

## Key Concepts (核心概念)

### Synchronous vs Asynchronous (同步 vs 异步)

```python
import time
import asyncio

# 同步代码
def sync_operation():
    print("开始同步操作")
    time.sleep(1)  # 阻塞操作
    print("同步操作完成")
    return "sync_result"

def sync_main():
    start = time.time()
    results = []
    for i in range(3):
        result = sync_operation()
        results.append(result)
    end = time.time()
    print(f"同步执行时间: {end - start:.2f}s")

# 异步代码
async def async_operation():
    print("开始异步操作")
    await asyncio.sleep(1)  # 非阻塞操作
    print("异步操作完成")
    return "async_result"

async def async_main():
    start = time.time()
    tasks = []
    for i in range(3):
        task = asyncio.create_task(async_operation())
        tasks.append(task)
    
    results = await asyncio.gather(*tasks)
    end = time.time()
    print(f"异步执行时间: {end - start:.2f}s")

# 运行比较
if __name__ == "__main__":
    print("=== 同步执行 ===")
    sync_main()
    
    print("\n=== 异步执行 ===")
    asyncio.run(async_main())
```

### Coroutines (协程)

协程是可以暂停和恢复执行的函数：

Coroutines are functions that can be paused and resumed:

```python
import asyncio

async def simple_coroutine():
    """简单的协程示例"""
    print("协程开始")
    await asyncio.sleep(1)
    print("协程结束")
    return "协程结果"

async def coroutine_with_parameters(name, delay):
    """带参数的协程"""
    print(f"协程 {name} 开始")
    await asyncio.sleep(delay)
    print(f"协程 {name} 结束")
    return f"协程 {name} 的结果"

async def main():
    # 单个协程
    result = await simple_coroutine()
    print(f"结果: {result}")
    
    # 多个协程并发执行
    tasks = [
        coroutine_with_parameters("A", 1),
        coroutine_with_parameters("B", 2),
        coroutine_with_parameters("C", 1.5)
    ]
    
    results = await asyncio.gather(*tasks)
    print(f"所有结果: {results}")

# 运行
asyncio.run(main())
```

### Event Loop (事件循环)

事件循环是异步程序的核心：

The event loop is the core of asynchronous programs:

```python
import asyncio

async def periodic_task(name, interval):
    """周期性任务"""
    count = 0
    while count < 5:
        print(f"任务 {name}: 执行第 {count + 1} 次")
        await asyncio.sleep(interval)
        count += 1

async def event_loop_example():
    # 获取当前事件循环
    loop = asyncio.get_running_loop()
    print(f"当前事件循环: {loop}")
    
    # 创建任务
    task1 = asyncio.create_task(periodic_task("快速", 0.5))
    task2 = asyncio.create_task(periodic_task("慢速", 1.0))
    
    # 等待任务完成
    await asyncio.gather(task1, task2)

# 运行
asyncio.run(event_loop_example())
```

## Practical Examples (实际示例)

### 1. 异步HTTP客户端 (Async HTTP Client)

```python
import asyncio
import aiohttp
import time

async def fetch_url(session, url):
    """异步获取URL内容"""
    try:
        async with session.get(url) as response:
            content = await response.text()
            return {
                'url': url,
                'status': response.status,
                'length': len(content)
            }
    except Exception as e:
        return {
            'url': url,
            'error': str(e)
        }

async def fetch_multiple_urls(urls):
    """并发获取多个URL"""
    async with aiohttp.ClientSession() as session:
        tasks = [fetch_url(session, url) for url in urls]
        results = await asyncio.gather(*tasks, return_exceptions=True)
        return results

# 使用示例
async def http_client_example():
    urls = [
        'https://httpbin.org/delay/1',
        'https://httpbin.org/delay/2',
        'https://httpbin.org/delay/1',
    ]
    
    start = time.time()
    results = await fetch_multiple_urls(urls)
    end = time.time()
    
    print(f"获取 {len(urls)} 个URL耗时: {end - start:.2f}s")
    for result in results:
        if isinstance(result, dict) and 'error' not in result:
            print(f"URL: {result['url']}, 状态: {result['status']}, 长度: {result['length']}")
        else:
            print(f"错误: {result}")

# 注意：需要安装aiohttp: pip install aiohttp
# asyncio.run(http_client_example())
```

### 2. 异步文件处理 (Async File Processing)

```python
import asyncio
import aiofiles
import os

async def read_file_async(filename):
    """异步读取文件"""
    try:
        async with aiofiles.open(filename, 'r', encoding='utf-8') as file:
            content = await file.read()
            return {
                'filename': filename,
                'size': len(content),
                'lines': len(content.splitlines())
            }
    except Exception as e:
        return {
            'filename': filename,
            'error': str(e)
        }

async def write_file_async(filename, content):
    """异步写入文件"""
    try:
        async with aiofiles.open(filename, 'w', encoding='utf-8') as file:
            await file.write(content)
            return {'filename': filename, 'status': 'success'}
    except Exception as e:
        return {'filename': filename, 'error': str(e)}

async def process_files_async(filenames):
    """并发处理多个文件"""
    # 并发读取文件
    read_tasks = [read_file_async(filename) for filename in filenames]
    read_results = await asyncio.gather(*read_tasks)
    
    # 并发写入处理结果
    write_tasks = []
    for i, result in enumerate(read_results):
        if 'error' not in result:
            output_filename = f"processed_{i}.txt"
            content = f"文件: {result['filename']}\n大小: {result['size']}\n行数: {result['lines']}\n"
            write_tasks.append(write_file_async(output_filename, content))
    
    write_results = await asyncio.gather(*write_tasks)
    return read_results, write_results

# 注意：需要安装aiofiles: pip install aiofiles
```

### 3. 异步生产者-消费者模式 (Async Producer-Consumer Pattern)

```python
import asyncio
import random

async def producer(queue, name, count):
    """生产者协程"""
    for i in range(count):
        # 模拟生产时间
        await asyncio.sleep(random.uniform(0.1, 0.5))
        
        item = f"{name}-item-{i}"
        await queue.put(item)
        print(f"生产者 {name} 生产了: {item}")
    
    print(f"生产者 {name} 完成")

async def consumer(queue, name):
    """消费者协程"""
    while True:
        try:
            # 等待队列中的项目
            item = await asyncio.wait_for(queue.get(), timeout=2.0)
            
            # 模拟处理时间
            await asyncio.sleep(random.uniform(0.2, 0.8))
            
            print(f"消费者 {name} 处理了: {item}")
            queue.task_done()
            
        except asyncio.TimeoutError:
            print(f"消费者 {name} 超时，退出")
            break

async def producer_consumer_example():
    # 创建队列
    queue = asyncio.Queue(maxsize=5)
    
    # 创建生产者任务
    producers = [
        asyncio.create_task(producer(queue, "P1", 5)),
        asyncio.create_task(producer(queue, "P2", 3))
    ]
    
    # 创建消费者任务
    consumers = [
        asyncio.create_task(consumer(queue, "C1")),
        asyncio.create_task(consumer(queue, "C2"))
    ]
    
    # 等待所有生产者完成
    await asyncio.gather(*producers)
    
    # 等待队列中的所有任务完成
    await queue.join()
    
    # 取消消费者任务
    for c in consumers:
        c.cancel()
    
    print("生产者-消费者示例完成")

# 运行示例
asyncio.run(producer_consumer_example())
```

## Async Context Managers (异步上下文管理器)

```python
import asyncio

class AsyncDatabaseConnection:
    """异步数据库连接示例"""
    
    def __init__(self, host, port):
        self.host = host
        self.port = port
        self.connection = None
    
    async def __aenter__(self):
        print(f"连接到数据库 {self.host}:{self.port}")
        await asyncio.sleep(0.1)  # 模拟连接时间
        self.connection = f"connection_to_{self.host}_{self.port}"
        return self.connection
    
    async def __aexit__(self, exc_type, exc_val, exc_tb):
        print("关闭数据库连接")
        await asyncio.sleep(0.1)  # 模拟关闭时间
        self.connection = None
        
        if exc_type:
            print(f"处理异常: {exc_type.__name__}: {exc_val}")
        
        return False  # 不抑制异常

async def database_operation():
    async with AsyncDatabaseConnection("localhost", 5432) as conn:
        print(f"使用连接: {conn}")
        await asyncio.sleep(1)  # 模拟数据库操作
        print("数据库操作完成")

# 运行示例
asyncio.run(database_operation())
```

## Error Handling in Async Code (异步代码错误处理)

```python
import asyncio
import random

async def unreliable_operation(name):
    """不可靠的操作，可能失败"""
    await asyncio.sleep(random.uniform(0.1, 0.5))
    
    if random.random() < 0.3:  # 30%的失败率
        raise Exception(f"操作 {name} 失败")
    
    return f"操作 {name} 成功"

async def error_handling_example():
    tasks = []
    
    # 创建多个可能失败的任务
    for i in range(5):
        task = asyncio.create_task(unreliable_operation(f"task-{i}"))
        tasks.append(task)
    
    # 方法1: 使用gather处理异常
    try:
        results = await asyncio.gather(*tasks, return_exceptions=True)
        for i, result in enumerate(results):
            if isinstance(result, Exception):
                print(f"任务 {i} 失败: {result}")
            else:
                print(f"任务 {i} 成功: {result}")
    except Exception as e:
        print(f"意外错误: {e}")
    
    print("\n--- 使用as_completed处理 ---")
    
    # 方法2: 使用as_completed逐个处理
    new_tasks = [asyncio.create_task(unreliable_operation(f"task-{i}")) for i in range(3)]
    
    for completed_task in asyncio.as_completed(new_tasks):
        try:
            result = await completed_task
            print(f"完成: {result}")
        except Exception as e:
            print(f"失败: {e}")

# 运行示例
asyncio.run(error_handling_example())
```

## Performance Considerations (性能考虑)

### 1. 任务创建开销 (Task Creation Overhead)

```python
import asyncio
import time

async def simple_task():
    await asyncio.sleep(0.001)
    return "done"

async def performance_comparison():
    # 方法1: 逐个等待
    start = time.time()
    for _ in range(100):
        await simple_task()
    sequential_time = time.time() - start
    
    # 方法2: 并发执行
    start = time.time()
    tasks = [simple_task() for _ in range(100)]
    await asyncio.gather(*tasks)
    concurrent_time = time.time() - start
    
    print(f"顺序执行: {sequential_time:.4f}s")
    print(f"并发执行: {concurrent_time:.4f}s")
    print(f"性能提升: {sequential_time / concurrent_time:.2f}x")

asyncio.run(performance_comparison())
```

## Best Practices (最佳实践)

1. **使用asyncio.run()**: 作为异步程序的入口点
2. **避免阻塞操作**: 在异步代码中使用异步版本的函数
3. **合理使用并发**: 不要创建过多的并发任务
4. **错误处理**: 始终处理可能的异常
5. **资源管理**: 使用异步上下文管理器管理资源

1. **Use asyncio.run()**: As the entry point for async programs
2. **Avoid blocking operations**: Use async versions of functions in async code
3. **Use concurrency wisely**: Don't create too many concurrent tasks
4. **Error handling**: Always handle potential exceptions
5. **Resource management**: Use async context managers for resource management

## Common Pitfalls (常见陷阱)

- 在异步代码中使用同步阻塞操作
- 忘记使用await关键字
- 创建过多的并发任务导致资源耗尽
- 不正确的异常处理
- 混合使用同步和异步代码

- Using synchronous blocking operations in async code
- Forgetting to use the await keyword
- Creating too many concurrent tasks leading to resource exhaustion
- Incorrect exception handling
- Mixing synchronous and asynchronous code

让我们开始深入学习Python异步编程的各个方面！

Let's start diving deep into various aspects of Python asynchronous programming!
