# 3.4 Set Theory (集合论)

Sets are a powerful tool in Python for working with collections of unique items. They are particularly useful for performing mathematical set operations.

集合是Python中处理唯一项集合的强大工具。它们对于执行数学集合运算特别有用。

## Set Operations (集合运算)

Here are some of the most common set operations:

以下是一些最常见的集合运算：

*   **Union (并集):** `s1 | s2` - Returns a new set with elements from both `s1` and `s2`.
*   **Intersection (交集):** `s1 & s2` - Returns a new set with elements that are common to `s1` and `s2`.
*   **Difference (差集):** `s1 - s2` - Returns a new set with elements in `s1` that are not in `s2`.
*   **Symmetric Difference (对称差集):** `s1 ^ s2` - Returns a new set with elements in either `s1` or `s2` but not both.

```python
# code/set_operations.py

set1 = {1, 2, 3, 4, 5}
set2 = {4, 5, 6, 7, 8}

print(f"set1: {set1}")
print(f"set2: {set2}")
print(f"Union: {set1 | set2}")
print(f"Intersection: {set1 & set2}")
print(f"Difference: {set1 - set2}")
print(f"Symmetric Difference: {set1 ^ set2}")
```

## Set Comprehensions (集合推导)

Like lists and dictionaries, you can create sets using comprehensions.

与列表和字典一样，你可以使用推导来创建集合。

```python
# code/set_comprehension.py

my_list = [1, 2, 3, 2, 1, 4, 5, 4]
my_set = {x for x in my_list}
print(f"Original list: {my_list}")
print(f"Set from list: {my_set}")
```
