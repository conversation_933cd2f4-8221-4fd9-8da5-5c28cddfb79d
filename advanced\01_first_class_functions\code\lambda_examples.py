# code/lambda_examples.py

# A simple lambda function (一个简单的lambda函数)
add = lambda x, y: x + y
print(f"add(2, 3): {add(2, 3)}")

# Using lambda with sorted() (将lambda与sorted()一起使用)
students = [('<PERSON>', 'A', 15), ('<PERSON>', 'B', 12), ('<PERSON>', 'B', 10)]
sorted_students = sorted(students, key=lambda student: student[2])
print(f"Students: {students}")
print(f"Sorted students by age: {sorted_students}")
