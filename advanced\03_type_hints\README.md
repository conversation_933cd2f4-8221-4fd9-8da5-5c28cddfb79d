# Chapter 3: Type Hints in Functions (函数中的类型提示)

This chapter provides a more in-depth look at type hints in Python. We will cover advanced topics such as type aliases, using `Optional` for parameters that can be `None`, and how to use the `typing` module to create more complex type hints.

本章更深入地探讨了Python中的类型提示。我们将涵盖高级主题，例如类型别名、对可能为`None`的参数使用`Optional`，以及如何使用`typing`模块创建更复杂的类型提示。

## Table of Contents (目录)

1.  [Gradual Typing (逐渐类型化)](01_gradual_typing.md)
2.  [Type Aliases (类型别名)](02_type_aliases.md)
3.  [Optional and Union Types (`Optional`与`Union`类型)](03_optional_and_union_types.md)
4.  [Generic Collections (泛型集合)](04_generic_collections.md)
