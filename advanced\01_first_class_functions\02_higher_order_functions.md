# 1.2 Higher-Order Functions (高阶函数)

A higher-order function is a function that takes a function as an argument, returns a function, or both.

高阶函数是接受一个函数作为参数、返回一个函数或两者兼有的函数。

## `map`, `filter`, and `reduce`

Some of the most well-known higher-order functions are `map`, `filter`, and `reduce`.

一些最著名的高阶函数是 `map`、`filter` 和 `reduce`。

*   `map(function, iterable)`: Applies `function` to every item of `iterable` and returns an iterator of the results.
*   `filter(function, iterable)`: Constructs an iterator from elements of `iterable` for which `function` returns true.
*   `functools.reduce(function, iterable)`: Applies `function` of two arguments cumulatively to the items of `iterable`, from left to right, so as to reduce the iterable to a single value.

```python
# code/higher_order_functions.py

from functools import reduce

# map
numbers = [1, 2, 3, 4, 5]
squares = map(lambda x: x**2, numbers)
print(f"Squares: {list(squares)}")

# filter
evens = filter(lambda x: x % 2 == 0, numbers)
print(f"Evens: {list(evens)}")

# reduce
product = reduce(lambda x, y: x * y, numbers)
print(f"Product: {product}")
```

While `map` and `filter` are still widely used, list comprehensions and generator expressions are often considered more Pythonic for these tasks.

虽然 `map` 和 `filter` 仍然被广泛使用，但列表推导和生成器表达式通常被认为在这些任务中更具Pythonic风格。
