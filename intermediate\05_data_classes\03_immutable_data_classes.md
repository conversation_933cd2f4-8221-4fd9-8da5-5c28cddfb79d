# 5.3 Immutable Data Classes (不可变数据类)

You can make data class instances immutable by setting `frozen=True` in the `@dataclass` decorator. This is useful when you want to ensure that the data in an object cannot be changed after it is created.

你可以通过在 `@dataclass` 装饰器中设置 `frozen=True` 来使数据类实例不可变。当你希望确保对象中的数据在创建后不能被更改时，这很有用。

```python
# code/immutable_dataclass.py

from dataclasses import dataclass

@dataclass(frozen=True)
class ImmutableCard:
    rank: str
    suit: str

card = ImmutableCard('A', 'spades')

# This will raise a FrozenInstanceError
# (这将引发一个FrozenInstanceError)
try:
    card.rank = 'K'
except Exception as e:
    print(f"Error: {e}")
```

Frozen data classes are also hashable, which means you can use them as keys in a dictionary or as elements in a set.

冻结的数据类也是可哈希的，这意味着你可以将它们用作字典中的键或集合中的元素。

```python
# code/hashable_dataclass.py

from dataclasses import dataclass

@dataclass(frozen=True)
class HashableCard:
    rank: str
    suit: str

card1 = HashableCard('A', 'spades')
card2 = HashableCard('A', 'spades')

my_dict = {card1: "Ace of Spades"}
print(my_dict[card2])
```
