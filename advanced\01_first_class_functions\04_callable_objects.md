# 1.4 Callable Objects (可调用对象)

In Python, not only functions are callable. A callable object is any object that can be called using the `()` operator. Besides functions, there are other types of callable objects, such as classes and instances of classes that implement the `__call__` method.

在Python中，不仅函数是可调用的。可调用对象是任何可以使用 `()` 运算符调用的对象。除了函数之外，还有其他类型的可调用对象，例如类和实现了 `__call__` 方法的类的实例。

## The `callable()` built-in ( `callable()` 内置函数)

You can use the `callable()` built-in function to check if an object is callable.

你可以使用 `callable()` 内置函数来检查一个对象是否是可调用的。

```python
# code/callable_objects.py

print(f"callable(print): {callable(print)}")
print(f"callable(1): {callable(1)}")
```

## User-defined callable types (用户定义的可调用类型)

You can create your own callable objects by implementing the `__call__` special method in a class.

你可以通过在类中实现 `__call__` 特殊方法来创建自己的可调用对象。

```python
# code/bingo_cage.py

import random

class BingoCage:

    def __init__(self, items):
        self._items = list(items)
        random.shuffle(self._items)

    def pick(self):
        try:
            return self._items.pop()
        except IndexError:
            raise LookupError('pick from empty BingoCage')

    def __call__(self):
        return self.pick()

bingo = BingoCage(range(3))
print(f"bingo.pick(): {bingo.pick()}")
print(f"bingo(): {bingo()}")
print(f"callable(bingo): {callable(bingo)}")
```
