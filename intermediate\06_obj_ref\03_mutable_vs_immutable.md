# 6.3 Mutable vs. Immutable Types (可变类型与不可变类型)

Python objects can be categorized as either mutable or immutable.

Python对象可以分为可变或不可变。

*   **Mutable objects** can be changed after they are created. Examples include lists, dictionaries, and sets. (可变对象在创建后可以被更改。例子包括列表、字典和集合。)
*   **Immutable objects** cannot be changed after they are created. Examples include numbers, strings, and tuples. (不可变对象在创建后不能被更改。例子包括数字、字符串和元组。)

## The Perils of Mutable Default Arguments (可变默认参数的危险)

A common pitfall in Python is to use a mutable object as a default argument to a function. The default value is evaluated only once, when the function is defined. This means that if you modify the default argument, the modification will persist across function calls.

Python中的一个常见陷阱是使用可变对象作为函数的默认参数。默认值只在函数定义时被评估一次。这意味着如果你修改了默认参数，这个修改将在函数调用之间持续存在。

```python
# code/mutable_default_argument.py

class HauntedBus:
    """A bus model haunted by ghost passengers"""

    def __init__(self, passengers=[]):
        self.passengers = passengers

    def pick(self, name):
        self.passengers.append(name)

    def drop(self, name):
        self.passengers.remove(name)


bus1 = HauntedBus(['Alice', 'Bill'])
bus1.pick('Charlie')
print(f"bus1.passengers: {bus1.passengers}")

# The default passengers list is shared between all instances
# (默认的乘客列表在所有实例之间共享)
bus2 = HauntedBus()
bus2.pick('Carrie')
print(f"bus2.passengers: {bus2.passengers}")

bus3 = HauntedBus()
print(f"bus3.passengers: {bus3.passengers}")

bus3.pick('Dave')
print(f"bus2.passengers after bus3.pick('Dave'): {bus2.passengers}")
```

To avoid this problem, you should use `None` as the default value and create a new mutable object inside the function if the argument is `None`.

为了避免这个问题，你应该使用 `None` 作为默认值，并在函数内部如果参数是 `None` 时创建一个新的可变对象。

```python
# code/immutable_default_argument.py

class Bus:

    def __init__(self, passengers=None):
        if passengers is None:
            self.passengers = []
        else:
            self.passengers = list(passengers)

    def pick(self, name):
        self.passengers.append(name)

    def drop(self, name):
        self.passengers.remove(name)
```
