# code/type_aliases.py

from typing import List, Dict, Tuple

# A complex type hint (一个复杂的类型提示)
Vector = List[float]

# Another complex type hint (另一个复杂的类型提示)
Headers = Dict[str, str]

# A type hint for a tuple of a string and an integer
# (一个字符串和整数元组的类型提示)
Person = Tuple[str, int]

def scale(scalar: float, vector: Vector) -> Vector:
    return [scalar * num for num in vector]

def greet(name: str, headers: Headers) -> None:
    print(f"Hello, {name}!")
    for key, value in headers.items():
        print(f"{key}: {value}")


person: Person = ("Alice", 30)
vector: Vector = [1.0, 2.0, 3.0]
headers: Headers = {"Content-Type": "application/json"}

print(f"scale(2.0, {vector}) = {scale(2.0, vector)}")
greet(person[0], headers)
