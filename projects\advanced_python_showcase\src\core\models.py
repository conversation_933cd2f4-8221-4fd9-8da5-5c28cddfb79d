#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Core data models demonstrating advanced Python features
核心数据模型，展示Python高级特性

This module showcases:
- Metaclasses for automatic model registration
- Descriptors for field validation
- Dataclasses with advanced features
- Enum usage for type safety
- Type hints for better code documentation
"""

from datetime import datetime
from typing import Optional, List, Dict, Any, Type, ClassVar
from enum import Enum, auto
from dataclasses import dataclass, field
import json
import weakref
from abc import ABC, abstractmethod


# ============================================================================
# 枚举定义 (Enum Definitions)
# ============================================================================

class TaskStatus(Enum):
    """任务状态枚举"""
    PENDING = "pending"
    IN_PROGRESS = "in_progress"
    COMPLETED = "completed"
    CANCELLED = "cancelled"
    
    def __str__(self):
        return self.value
    
    @classmethod
    def from_string(cls, status_str: str) -> 'TaskStatus':
        """从字符串创建状态"""
        for status in cls:
            if status.value == status_str.lower():
                return status
        raise ValueError(f"Invalid status: {status_str}")


class Priority(Enum):
    """优先级枚举"""
    LOW = 1
    MEDIUM = 2
    HIGH = 3
    URGENT = 4
    CRITICAL = 5
    
    def __lt__(self, other):
        if isinstance(other, Priority):
            return self.value < other.value
        return NotImplemented
    
    def __str__(self):
        return self.name.title()


class UserRole(Enum):
    """用户角色枚举"""
    GUEST = auto()
    USER = auto()
    MODERATOR = auto()
    ADMIN = auto()
    SUPER_ADMIN = auto()


# ============================================================================
# 描述符 (Descriptors)
# ============================================================================

class ValidatedField:
    """验证字段描述符"""
    
    def __init__(self, validator=None, default=None, required=True):
        self.validator = validator
        self.default = default
        self.required = required
        self.name = None
    
    def __set_name__(self, owner, name):
        self.name = name
        self.private_name = f'_{name}'
    
    def __get__(self, obj, objtype=None):
        if obj is None:
            return self
        return getattr(obj, self.private_name, self.default)
    
    def __set__(self, obj, value):
        if value is None and self.required:
            raise ValueError(f"{self.name} is required")
        
        if value is not None and self.validator:
            if not self.validator(value):
                raise ValueError(f"Invalid value for {self.name}: {value}")
        
        setattr(obj, self.private_name, value)


class TypedField:
    """类型检查字段描述符"""
    
    def __init__(self, field_type, default=None, allow_none=False):
        self.field_type = field_type
        self.default = default
        self.allow_none = allow_none
        self.name = None
    
    def __set_name__(self, owner, name):
        self.name = name
        self.private_name = f'_{name}'
    
    def __get__(self, obj, objtype=None):
        if obj is None:
            return self
        return getattr(obj, self.private_name, self.default)
    
    def __set__(self, obj, value):
        if value is None:
            if not self.allow_none:
                raise ValueError(f"{self.name} cannot be None")
        elif not isinstance(value, self.field_type):
            raise TypeError(f"{self.name} must be of type {self.field_type.__name__}")
        
        setattr(obj, self.private_name, value)


# ============================================================================
# 元类 (Metaclasses)
# ============================================================================

class ModelMeta(type):
    """模型元类，用于自动注册模型和添加功能"""
    
    # 模型注册表
    _registry: Dict[str, Type] = {}
    
    def __new__(mcs, name, bases, namespace, **kwargs):
        # 添加模型验证方法
        if not any(hasattr(base, 'validate') for base in bases):
            namespace['validate'] = mcs._create_validate_method()
        
        # 添加序列化方法
        if not any(hasattr(base, 'to_dict') for base in bases):
            namespace['to_dict'] = mcs._create_to_dict_method()
        
        # 添加反序列化类方法
        if not any(hasattr(base, 'from_dict') for base in bases):
            namespace['from_dict'] = mcs._create_from_dict_method()
        
        cls = super().__new__(mcs, name, bases, namespace)
        
        # 注册模型
        if name != 'BaseModel':  # 不注册基类
            mcs._registry[name] = cls
        
        return cls
    
    @staticmethod
    def _create_validate_method():
        """创建验证方法"""
        def validate(self):
            """验证模型数据"""
            errors = []
            for attr_name in dir(self):
                attr = getattr(type(self), attr_name, None)
                if isinstance(attr, (ValidatedField, TypedField)):
                    try:
                        # 触发描述符的验证
                        value = getattr(self, attr_name)
                        setattr(self, attr_name, value)
                    except (ValueError, TypeError) as e:
                        errors.append(str(e))
            
            if errors:
                raise ValueError(f"Validation errors: {'; '.join(errors)}")
            return True
        
        return validate
    
    @staticmethod
    def _create_to_dict_method():
        """创建序列化方法"""
        def to_dict(self):
            """转换为字典"""
            result = {}
            for attr_name in dir(self):
                if not attr_name.startswith('_') and not callable(getattr(self, attr_name)):
                    value = getattr(self, attr_name)
                    if isinstance(value, Enum):
                        result[attr_name] = value.value
                    elif isinstance(value, datetime):
                        result[attr_name] = value.isoformat()
                    elif hasattr(value, 'to_dict'):
                        result[attr_name] = value.to_dict()
                    else:
                        result[attr_name] = value
            return result
        
        return to_dict
    
    @staticmethod
    def _create_from_dict_method():
        """创建反序列化类方法"""
        @classmethod
        def from_dict(cls, data: Dict[str, Any]):
            """从字典创建实例"""
            kwargs = {}
            for key, value in data.items():
                # 处理特殊类型
                if hasattr(cls, key):
                    attr = getattr(cls, key)
                    if isinstance(attr, ValidatedField):
                        kwargs[key] = value
                    elif isinstance(attr, TypedField):
                        if attr.field_type == datetime and isinstance(value, str):
                            kwargs[key] = datetime.fromisoformat(value)
                        else:
                            kwargs[key] = value
                    else:
                        kwargs[key] = value
                else:
                    kwargs[key] = value
            
            return cls(**kwargs)
        
        return from_dict
    
    @classmethod
    def get_registered_models(mcs):
        """获取所有注册的模型"""
        return mcs._registry.copy()


# ============================================================================
# 基础模型类 (Base Model Classes)
# ============================================================================

class BaseModel(metaclass=ModelMeta):
    """基础模型类"""
    
    def __init__(self, **kwargs):
        for key, value in kwargs.items():
            setattr(self, key, value)
    
    def __repr__(self):
        class_name = self.__class__.__name__
        attrs = []
        for attr_name in dir(self):
            if not attr_name.startswith('_') and not callable(getattr(self, attr_name)):
                value = getattr(self, attr_name)
                attrs.append(f"{attr_name}={value!r}")
        return f"{class_name}({', '.join(attrs)})"
    
    def __eq__(self, other):
        if not isinstance(other, self.__class__):
            return False
        
        for attr_name in dir(self):
            if not attr_name.startswith('_') and not callable(getattr(self, attr_name)):
                if getattr(self, attr_name) != getattr(other, attr_name):
                    return False
        return True


# ============================================================================
# 具体模型类 (Concrete Model Classes)
# ============================================================================

class User(BaseModel):
    """用户模型"""
    
    id: int = ValidatedField(validator=lambda x: x > 0)
    username: str = ValidatedField(validator=lambda x: len(x.strip()) >= 3)
    email: str = ValidatedField(validator=lambda x: '@' in x and '.' in x)
    role: UserRole = TypedField(UserRole, default=UserRole.USER)
    created_at: datetime = TypedField(datetime, default_factory=datetime.now)
    is_active: bool = TypedField(bool, default=True)
    
    def __init__(self, id: int, username: str, email: str, 
                 role: UserRole = UserRole.USER, **kwargs):
        self.id = id
        self.username = username
        self.email = email
        self.role = role
        self.created_at = kwargs.get('created_at', datetime.now())
        self.is_active = kwargs.get('is_active', True)
    
    def has_permission(self, required_role: UserRole) -> bool:
        """检查用户是否有指定权限"""
        return self.role.value >= required_role.value


class Task(BaseModel):
    """任务模型"""
    
    id: int = ValidatedField(validator=lambda x: x > 0)
    title: str = ValidatedField(validator=lambda x: len(x.strip()) > 0)
    description: str = TypedField(str, allow_none=True)
    status: TaskStatus = TypedField(TaskStatus, default=TaskStatus.PENDING)
    priority: Priority = TypedField(Priority, default=Priority.MEDIUM)
    assignee_id: int = ValidatedField(validator=lambda x: x > 0, required=False)
    created_by: int = ValidatedField(validator=lambda x: x > 0)
    created_at: datetime = TypedField(datetime, default_factory=datetime.now)
    updated_at: datetime = TypedField(datetime, allow_none=True)
    due_date: datetime = TypedField(datetime, allow_none=True)
    dependencies: List[int] = field(default_factory=list)
    tags: List[str] = field(default_factory=list)
    metadata: Dict[str, Any] = field(default_factory=dict)
    
    def __init__(self, id: int, title: str, created_by: int, **kwargs):
        self.id = id
        self.title = title
        self.created_by = created_by
        self.description = kwargs.get('description')
        self.status = kwargs.get('status', TaskStatus.PENDING)
        self.priority = kwargs.get('priority', Priority.MEDIUM)
        self.assignee_id = kwargs.get('assignee_id')
        self.created_at = kwargs.get('created_at', datetime.now())
        self.updated_at = kwargs.get('updated_at')
        self.due_date = kwargs.get('due_date')
        self.dependencies = kwargs.get('dependencies', [])
        self.tags = kwargs.get('tags', [])
        self.metadata = kwargs.get('metadata', {})
    
    def is_overdue(self) -> bool:
        """检查任务是否过期"""
        if self.due_date is None:
            return False
        return datetime.now() > self.due_date and self.status != TaskStatus.COMPLETED
    
    def can_be_completed(self, all_tasks: Dict[int, 'Task']) -> bool:
        """检查任务是否可以完成（所有依赖都已完成）"""
        for dep_id in self.dependencies:
            dep_task = all_tasks.get(dep_id)
            if dep_task is None or dep_task.status != TaskStatus.COMPLETED:
                return False
        return True
    
    def add_dependency(self, task_id: int):
        """添加依赖任务"""
        if task_id not in self.dependencies:
            self.dependencies.append(task_id)
            self.updated_at = datetime.now()
    
    def remove_dependency(self, task_id: int):
        """移除依赖任务"""
        if task_id in self.dependencies:
            self.dependencies.remove(task_id)
            self.updated_at = datetime.now()


class Project(BaseModel):
    """项目模型"""
    
    id: int = ValidatedField(validator=lambda x: x > 0)
    name: str = ValidatedField(validator=lambda x: len(x.strip()) > 0)
    description: str = TypedField(str, allow_none=True)
    owner_id: int = ValidatedField(validator=lambda x: x > 0)
    created_at: datetime = TypedField(datetime, default_factory=datetime.now)
    updated_at: datetime = TypedField(datetime, allow_none=True)
    is_active: bool = TypedField(bool, default=True)
    task_ids: List[int] = field(default_factory=list)
    member_ids: List[int] = field(default_factory=list)
    
    def __init__(self, id: int, name: str, owner_id: int, **kwargs):
        self.id = id
        self.name = name
        self.owner_id = owner_id
        self.description = kwargs.get('description')
        self.created_at = kwargs.get('created_at', datetime.now())
        self.updated_at = kwargs.get('updated_at')
        self.is_active = kwargs.get('is_active', True)
        self.task_ids = kwargs.get('task_ids', [])
        self.member_ids = kwargs.get('member_ids', [])
    
    def add_task(self, task_id: int):
        """添加任务到项目"""
        if task_id not in self.task_ids:
            self.task_ids.append(task_id)
            self.updated_at = datetime.now()
    
    def remove_task(self, task_id: int):
        """从项目中移除任务"""
        if task_id in self.task_ids:
            self.task_ids.remove(task_id)
            self.updated_at = datetime.now()
    
    def add_member(self, user_id: int):
        """添加成员到项目"""
        if user_id not in self.member_ids:
            self.member_ids.append(user_id)
            self.updated_at = datetime.now()
    
    def remove_member(self, user_id: int):
        """从项目中移除成员"""
        if user_id in self.member_ids:
            self.member_ids.remove(user_id)
            self.updated_at = datetime.now()


# ============================================================================
# 模型工厂 (Model Factory)
# ============================================================================

class ModelFactory:
    """模型工厂类"""
    
    @staticmethod
    def create_model(model_name: str, **kwargs) -> BaseModel:
        """根据名称创建模型实例"""
        model_class = ModelMeta.get_registered_models().get(model_name)
        if model_class is None:
            raise ValueError(f"Unknown model: {model_name}")
        
        return model_class(**kwargs)
    
    @staticmethod
    def get_available_models() -> List[str]:
        """获取所有可用的模型名称"""
        return list(ModelMeta.get_registered_models().keys())


# ============================================================================
# 使用示例 (Usage Examples)
# ============================================================================

if __name__ == "__main__":
    # 创建用户
    user = User(
        id=1,
        username="john_doe",
        email="<EMAIL>",
        role=UserRole.USER
    )
    
    # 创建任务
    task = Task(
        id=1,
        title="完成Python教程项目",
        description="实现一个展示Python高级特性的综合项目",
        created_by=user.id,
        priority=Priority.HIGH,
        tags=["python", "tutorial", "advanced"]
    )
    
    # 验证模型
    user.validate()
    task.validate()
    
    # 序列化
    user_dict = user.to_dict()
    task_dict = task.to_dict()
    
    print("用户数据:", json.dumps(user_dict, indent=2, ensure_ascii=False))
    print("任务数据:", json.dumps(task_dict, indent=2, ensure_ascii=False))
    
    # 反序列化
    user_copy = User.from_dict(user_dict)
    task_copy = Task.from_dict(task_dict)
    
    print(f"用户相等: {user == user_copy}")
    print(f"任务相等: {task == task_copy}")
    
    # 使用工厂创建模型
    factory = ModelFactory()
    print("可用模型:", factory.get_available_models())
    
    new_user = factory.create_model("User", id=2, username="jane_doe", email="<EMAIL>")
    print("工厂创建的用户:", new_user)
