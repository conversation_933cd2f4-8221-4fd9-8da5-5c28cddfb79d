# 6. Data Structures: Lists, Tuples, and Dictionaries (数据结构：列表、元组和字典)

Python has several built-in data structures that you can use to store and organize your data. In this chapter, we'll cover three of the most common data structures: lists, tuples, and dictionaries.

Python有几种内置的数据结构，你可以用它们来存储和组织你的数据。在本章中，我们将介绍三种最常见的数据结构：列表、元组和字典。

## Lists (列表)

A list is a collection of items that are ordered and mutable (meaning you can change them). Lists are created using square brackets `[]`.

列表是一个有序且可变（意味着你可以更改它们）的项目集合。列表使用方括号 `[]` 创建。

```python
# Creating a list (创建列表)
fruits = ["apple", "banana", "cherry"]

# Accessing items (访问项目)
print(fruits[0])  # apple

# Changing an item (更改项目)
fruits[1] = "orange"
print(fruits)  # ['apple', 'orange', 'cherry']

# Adding an item (添加项目)
fruits.append("grape")
print(fruits)  # ['apple', 'orange', 'cherry', 'grape']

# Removing an item (删除项目)
fruits.remove("apple")
print(fruits)  # ['orange', 'cherry', 'grape']

# Length of a list (列表长度)
print(len(fruits))  # 3
```

## Tuples (元组)

A tuple is a collection of items that are ordered and immutable (meaning you can't change them). Tuples are created using parentheses `()`.

元组是一个有序且不可变（意味着你不能更改它们）的项目集合。元组使用圆括号 `()` 创建。

```python
# Creating a tuple (创建元组)
coordinates = (10, 20)

# Accessing items (访问项目)
print(coordinates[0])  # 10

# Trying to change an item will raise a TypeError
# (尝试更改项目会引发TypeError)
# coordinates[0] = 15  # This will cause an error
```

## Dictionaries (字典)

A dictionary is a collection of key-value pairs. Dictionaries are created using curly braces `{}`.

字典是一个键值对的集合。字典使用花括号 `{}` 创建。

```python
# Creating a dictionary (创建字典)
person = {"name": "John", "age": 30}

# Accessing a value (访问值)
print(person["name"])  # John

# Changing a value (更改值)
person["age"] = 31
print(person)  # {'name': 'John', 'age': 31}

# Adding a new key-value pair (添加新的键值对)
person["city"] = "New York"
print(person)  # {'name': 'John', 'age': 31, 'city': 'New York'}

# Removing a key-value pair (删除键值对)
del person["age"]
print(person)  # {'name': 'John', 'city': 'New York'}
```