# code/inheritance_dataclass.py

from dataclasses import dataclass

@dataclass
class Person:
    name: str
    age: int

@dataclass
class Employee(Person):
    employee_id: int
    department: str = "Engineering"


person = Person("<PERSON>", 30)
employee = Employee("<PERSON>", 40, 12345)

print(f"Person: {person}")
print(f"Employee: {employee}")

# The Employee class has access to the fields of the Person class
# (Employee类可以访问Person类的字段)
print(f"Employee name: {employee.name}")
