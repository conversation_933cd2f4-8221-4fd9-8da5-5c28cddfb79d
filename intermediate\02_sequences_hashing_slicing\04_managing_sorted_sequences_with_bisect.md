# 2.4 Managing Sorted Sequences with `bisect` (使用`bisect`管理已排序序列)

The `bisect` module provides support for maintaining a list in sorted order without having to sort the list after each insertion.

`bisect` 模块支持在不每次插入后都对列表进行排序的情况下，保持列表的有序性。

## `bisect.bisect` and `bisect.insort`

*   `bisect.bisect(haystack, needle)`: returns an insertion point which comes after (to the right of) any existing entries of `needle` in `haystack`. (返回一个插入点，该插入点位于`haystack`中任何现有`needle`条目的后面（右侧）。)
*   `bisect.insort(seq, item)`: inserts `item` into `seq` so that `seq` remains sorted. (将`item`插入`seq`，以使`seq`保持排序。)

```python
# code/bisect_examples.py

import bisect

HAYSTACK = [1, 4, 5, 6, 8, 12, 15, 20, 21, 23, 23, 26, 29, 30]
NEEDLES = [0, 1, 2, 5, 8, 10, 22, 23, 29, 30, 31]

ROW_FMT = '{0:2d} @ {1:2d}    {2}{0:<2d}'

def demo(bisect_fn):
    for needle in reversed(NEEDLES):
        position = bisect_fn(HAYSTACK, needle)
        offset = position * '  |'
        print(ROW_FMT.format(needle, position, offset))

if __name__ == '__main__':
    print('--- bisect ---')
    demo(bisect.bisect)
    print('\n--- bisect_left ---')
    demo(bisect.bisect_left)

    print("\n--- insort ---")
    my_list = []
    for i in [5, 2, 8, 1, 9]:
        bisect.insort(my_list, i)
        print(f"insort {i}: {my_list}")
```