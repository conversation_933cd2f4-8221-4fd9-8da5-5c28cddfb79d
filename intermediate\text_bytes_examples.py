# text_bytes_examples.py

# str for text (str用于文本)
s = 'hello'
print(f"s = {s}, type is {type(s)}")
s_chinese = '你好'
print(f"s_chinese = {s_chinese}, type is {type(s_chinese)}")

# bytes for binary data (bytes用于二进制数据)
b = b'hello'
print(f"b = {b}, type is {type(b)}")

# Encoding and Decoding (编码与解码)
print("\n--- Encoding and Decoding ---")
s_to_encode = 'hello 你好'
print(f"Original string: {s_to_encode}")

utf8_encoded = s_to_encode.encode('utf-8')
print(f"UTF-8 encoded: {utf8_encoded}")

utf16_encoded = s_to_encode.encode('utf-16')
print(f"UTF-16 encoded: {utf16_encoded}")

# Decoding (解码)
print(f"Decoded from UTF-8: {utf8_encoded.decode('utf-8')}")
print(f"Decoded from UTF-16: {utf16_encoded.decode('utf-16')}")

