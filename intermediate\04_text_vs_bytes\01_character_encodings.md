# 4.1 Character Encodings (字符编码)

The `str` type in Python is a sequence of Unicode characters. To store or transmit a string, you need to encode it into a sequence of bytes. The `bytes` type represents these raw bytes.

Python中的 `str` 类型是Unicode字符的序列。要存储或传输字符串，你需要将其编码为字节序列。`bytes` 类型表示这些原始字节。

## Encoding and Decoding (编码与解码)

*   **Encoding:** Converting a `str` to `bytes`.
*   **Decoding:** Converting `bytes` to `str`.

```python
# code/encoding_decoding.py

# Encoding a string to bytes (将字符串编码为字节)
s = "café"
print(f"Original string: {s}")

utf8_bytes = s.encode('utf-8')
print(f"UTF-8 encoded: {utf8_bytes}")

# Decoding bytes to a string (将字节解码为字符串)
decoded_s = utf8_bytes.decode('utf-8')
print(f"Decoded from UTF-8: {decoded_s}")
```

## Common Encodings (常用编码)

*   **UTF-8:** The most common encoding on the web. It can represent any Unicode character.
*   **UTF-16:** Another common encoding, used by many operating systems.
*   **Latin-1 (ISO-8859-1):** A single-byte encoding that can represent the first 256 Unicode characters.

It is crucial to use the correct encoding when working with text data. Using the wrong encoding can lead to errors or corrupted data.

在使用文本数据时，使用正确的编码至关重要。使用错误的编码可能会导致错误或数据损坏。
