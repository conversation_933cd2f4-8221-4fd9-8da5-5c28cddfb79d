# code/genexp_examples.py

# A generator expression (一个生成器表达式)
squares_gen = (x**2 for x in range(10))
print(f"Squares generator: {squares_gen}")

# To get the values, you can iterate over the generator
# (要获取值，你可以遍历生成器)
print("Values from the generator expression:")
for square in squares_gen:
    print(square, end=" ")
print()

# You can also create a list from a generator expression
# (你也可以从生成器表达式创建一个列表)
squares_list = list(x**2 for x in range(5))
print(f"List from generator expression: {squares_list}")
