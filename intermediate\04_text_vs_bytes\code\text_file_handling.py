# code/text_file_handling.py

# Writing to a text file with a specific encoding
# (使用特定编码写入文本文件)
with open('hello.txt', 'w', encoding='utf-8') as f:
    f.write('你好, world!')

# Reading from a text file with the correct encoding
# (使用正确的编码从文本文件中读取)
with open('hello.txt', 'r', encoding='utf-8') as f:
    content = f.read()
    print(f"File content: {content}")

# Trying to read with the wrong encoding will raise an error
# (尝试使用错误的编码读取会引发错误)
try:
    with open('hello.txt', 'r', encoding='ascii') as f:
        content = f.read()
except UnicodeDecodeError as e:
    print(f"\nError reading with ASCII encoding: {e}")
