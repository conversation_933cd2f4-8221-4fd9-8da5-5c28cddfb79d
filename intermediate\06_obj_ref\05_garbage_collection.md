# 6.5 Garbage Collection (垃圾回收)

Python uses a mechanism called garbage collection to automatically manage memory. When an object is no longer referenced by any variable, it is eligible for garbage collection, and the memory it occupies can be reclaimed.

Python使用一种称为垃圾回收的机制来自动管理内存。当一个对象不再被任何变量引用时，它就有资格被垃圾回收，它所占用的内存就可以被回收。

## Reference Counting (引用计数)

The primary garbage collection mechanism in CPython is reference counting. Every object has a reference count, which is the number of variables that refer to it. When the reference count of an object drops to zero, the object is deallocated.

CPython中的主要垃圾回收机制是引用计数。每个对象都有一个引用计数，即引用它的变量的数量。当一个对象的引用计数降到零时，该对象就会被释放。

```python
# code/reference_counting.py

import sys

a = [1, 2, 3]
print(f"Reference count of a: {sys.getrefcount(a)}")

b = a
print(f"Reference count of a after b = a: {sys.getrefcount(a)}")

del b
print(f"Reference count of a after del b: {sys.getrefcount(a)}")
```

## Cyclic Garbage Collection (循环垃圾回收)

Reference counting alone is not enough to handle all cases. For example, if two objects refer to each other, they will have a reference count of at least 1, even if they are not reachable from anywhere else in the program. This is called a reference cycle.

单靠引用计数不足以处理所有情况。例如，如果两个对象相互引用，即使它们在程序中其他任何地方都不可达，它们的引用计数也至少为1。这被称为引用循环。

To deal with this, Python has a cyclic garbage collector that can detect and break reference cycles.

为了处理这个问题，Python有一个循环垃圾回收器，可以检测并打破引用循环。
