# code/hashable_dataclass.py

from dataclasses import dataclass

@dataclass(frozen=True)
class HashableCard:
    rank: str
    suit: str

card1 = HashableCard('A', 'spades')
card2 = HashableCard('A', 'spades')

# Because the data class is frozen, it is hashable
# (因为数据类是冻结的，所以它是可哈希的)
my_dict = {card1: "Ace of Spades"}

# The two cards are equal, so they have the same hash
# (这两张牌是相等的，所以它们有相同的哈希值)
print(f"my_dict[card2]: {my_dict[card2]}")
