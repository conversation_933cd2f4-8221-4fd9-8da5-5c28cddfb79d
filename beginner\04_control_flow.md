# 4. Control Flow (控制流)

Control flow is the order in which statements are executed in a program. In Python, you can use conditional statements and loops to control the flow of your program.

控制流是程序中语句执行的顺序。在Python中，你可以使用条件语句和循环来控制程序的流程。

## Conditional Statements (条件语句)

Conditional statements allow you to execute different blocks of code depending on whether a certain condition is true or false. The most common conditional statements are `if`, `elif`, and `else`.

条件语句允许你根据某个条件的真假来执行不同的代码块。最常见的条件语句是 `if`、`elif` 和 `else`。

### `if` Statement

```python
x = 10

if x > 5:
    print("x is greater than 5")
```

### `if-else` Statement

```python
x = 3

if x > 5:
    print("x is greater than 5")
else:
    print("x is not greater than 5")
```

### `if-elif-else` Statement

```python
x = 5

if x > 5:
    print("x is greater than 5")
elif x == 5:
    print("x is equal to 5")
else:
    print("x is less than 5")
```

## Loops (循环)

Loops allow you to execute a block of code multiple times. The two most common types of loops are `for` loops and `while` loops.

循环允许你多次执行一个代码块。两种最常见的循环类型是 `for` 循环和 `while` 循环。

### `for` Loops

A `for` loop is used to iterate over a sequence (such as a list, tuple, or string).

`for` 循环用于遍历一个序列（如列表、元组或字符串）。

```python
# Looping through a list (遍历列表)
fruits = ["apple", "banana", "cherry"]
for fruit in fruits:
    print(fruit)

# Looping through a string (遍历字符串)
for char in "hello":
    print(char)

# The range() function (range() 函数)
for i in range(5):  # from 0 to 4 (从0到4)
    print(i)
```

### `while` Loops

A `while` loop is used to execute a block of code as long as a certain condition is true.

`while` 循环用于在某个条件为真的情况下，重复执行一个代码块。

```python
i = 0
while i < 5:
    print(i)
    i += 1  # This is equivalent to i = i + 1 (这等同于 i = i + 1)
```

### `break` and `continue`

*   `break`: Exits the loop entirely. (完全退出循环。)
*   `continue`: Skips the current iteration and proceeds to the next. (跳过当前迭代，进入下一次迭代。)

```python
# break example
for i in range(10):
    if i == 5:
        break
    print(i)

# continue example
for i in range(10):
    if i % 2 == 0:  # if i is even (如果i是偶数)
        continue
    print(i)
```