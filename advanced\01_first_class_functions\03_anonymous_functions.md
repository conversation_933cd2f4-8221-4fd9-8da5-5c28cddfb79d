# 1.3 Anonymous Functions (匿名函数)

The `lambda` keyword in Python provides a shortcut for declaring small anonymous functions. `lambda` functions can be used wherever function objects are required.

Python中的 `lambda` 关键字为声明小型匿名函数提供了一种快捷方式。`lambda` 函数可以在任何需要函数对象的地方使用。

## `lambda` Syntax (lambda语法)

The syntax of a `lambda` function is:

`lambda` 函数的语法是：

```
lambda arguments: expression
```

The expression is executed and the result is returned.

表达式被执行并返回结果。

```python
# code/lambda_examples.py

# A simple lambda function (一个简单的lambda函数)
add = lambda x, y: x + y
print(f"add(2, 3): {add(2, 3)}")

# Using lambda with sorted() (将lambda与sorted()一起使用)
students = [('John', 'A', 15), ('Jane', 'B', 12), ('Dave', 'B', 10)]
sorted_students = sorted(students, key=lambda student: student[2])
print(f"Sorted students by age: {sorted_students}")
```

While `lambda` functions are a convenient way to write small, one-off functions, you should use a regular `def` statement for more complex functions.

虽然 `lambda` 函数是编写小型、一次性函数的便捷方式，但对于更复杂的函数，你应该使用常规的 `def` 语句。
