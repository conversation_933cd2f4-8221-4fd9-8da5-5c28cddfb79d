# code/basic_dataclass.py

from dataclasses import dataclass

@dataclass
class Card:
    rank: str
    suit: str

queen_of_hearts = Card('Q', 'hearts')
print(f"Card: {queen_of_hearts}")
print(f"Rank: {queen_of_hearts.rank}")
print(f"Suit: {queen_of_hearts.suit}")

ace_of_spades = Card('A', 'spades')
print(f"Is queen_of_hearts == ace_of_spades? {queen_of_hearts == ace_of_spades}")

ace_of_spades_clone = Card('A', 'spades')
print(f"Is ace_of_spades == ace_of_spades_clone? {ace_of_spades == ace_of_spades_clone}")
