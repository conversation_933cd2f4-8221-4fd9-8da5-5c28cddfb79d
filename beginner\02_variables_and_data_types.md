# 2. Variables and Data Types (变量与数据类型)

In Python, variables are used to store data. You can think of a variable as a named container for a value. You can assign a value to a variable using the assignment operator (`=`).

在Python中，变量用于存储数据。你可以把变量看作是存放值的命名容器。你可以使用赋值运算符 (`=`) 给变量赋值。

## Variables (变量)

Here's an example of how to create a variable in Python:

下面是一个在Python中创建变量的例子：

```python
x = 10
```

In this example, we've created a variable named `x` and assigned it the value `10`.

在这个例子中，我们创建了一个名为 `x` 的变量，并给它赋了值 `10`。

## Data Types (数据类型)

Python has several built-in data types. Here are some of the most common ones:

Python有几种内置的数据类型。以下是一些最常见的：

*   **Integers (整数):** Whole numbers, such as `10`, `-5`, and `1000`.
*   **Floats (浮点数):** Numbers with a decimal point, such as `3.14`, `-0.5`, and `2.718`.
*   **Strings (字符串):** Sequences of characters, enclosed in single or double quotes, such as `'hello'` and `"world"`.
*   **Booleans (布尔值):** Represent either `True` or `False`.

Here are some examples of how to create variables with different data types:

下面是一些创建不同数据类型变量的例子：

```python
# Integer (整数)
x = 10

# Float (浮点数)
y = 3.14

# String (字符串)
z = "hello"

# Boolean (布尔值)
a = True

# You can print the values of these variables
# (你可以打印这些变量的值)
print("x is", x)
print("y is", y)
print("z is", z)
print("a is", a)
```

### Checking Data Types (检查数据类型)

You can use the `type()` function to check the data type of a variable:

你可以使用 `type()` 函数来检查一个变量的数据类型：

```python
x = 10
print(type(x))  # <class 'int'>

y = 3.14
print(type(y))  # <class 'float'>

z = "hello"
print(type(z))  # <class 'str'>

a = True
print(type(a))  # <class 'bool'>
```

### Type Casting (类型转换)

You can convert between different data types using type casting functions like `int()`, `float()`, and `str()`.

你可以使用 `int()`、`float()` 和 `str()` 等类型转换函数在不同的数据类型之间进行转换。

```python
x = 10.5
print(int(x))  # 10

y = 20
print(float(y))  # 20.0

z = 100
print(str(z))  # "100"
```

## 深入理解数据类型 | Deep Dive into Data Types

### 数字类型详解 | Numeric Types in Detail

Python中的数字类型不仅仅是int和float，还包括复数：

```python
# 整数 (Integer)
age = 25
population = 1_000_000  # 使用下划线提高可读性

# 浮点数 (Float)
pi = 3.14159
scientific_notation = 1.23e-4  # 科学计数法

# 复数 (Complex)
complex_num = 3 + 4j
print(f"实部: {complex_num.real}, 虚部: {complex_num.imag}")

# 数字系统
binary = 0b1010      # 二进制 (10)
octal = 0o12         # 八进制 (10)
hexadecimal = 0xa    # 十六进制 (10)

print(f"二进制 {binary}, 八进制 {octal}, 十六进制 {hexadecimal}")
```

### 字符串类型详解 | String Types in Detail

```python
# 不同的字符串定义方式
single_quote = 'Hello'
double_quote = "World"
triple_quote = """这是一个
多行字符串"""

# 原始字符串 (Raw String)
raw_string = r"C:\Users\<USER>\Documents"  # 不转义反斜杠

# f-字符串 (格式化字符串)
name = "Alice"
age = 30
formatted = f"我的名字是 {name}，今年 {age} 岁"

# 字符串方法
text = "  Python Programming  "
print(f"原始: '{text}'")
print(f"去空格: '{text.strip()}'")
print(f"大写: '{text.upper()}'")
print(f"小写: '{text.lower()}'")
print(f"替换: '{text.replace('Python', 'Java')}'")
```

### 布尔类型与真值测试 | Boolean and Truth Value Testing

```python
# 布尔值
is_student = True
is_working = False

# 真值测试 - 以下值被认为是False
falsy_values = [
    False,      # 布尔False
    0,          # 数字零
    0.0,        # 浮点零
    "",         # 空字符串
    [],         # 空列表
    {},         # 空字典
    set(),      # 空集合
    None        # None值
]

for value in falsy_values:
    print(f"{repr(value)} is {'True' if value else 'False'}")

# 其他所有值都被认为是True
truthy_values = [1, "hello", [1, 2, 3], {"key": "value"}]
for value in truthy_values:
    print(f"{repr(value)} is {'True' if value else 'False'}")
```

## 变量命名规范 | Variable Naming Conventions

### PEP 8 命名规范

```python
# ✅ 好的命名
user_name = "Alice"
total_count = 100
is_valid = True
MAX_SIZE = 1000  # 常量使用大写

# ❌ 不好的命名
userName = "Alice"    # 驼峰命名不符合Python规范
totalcount = 100      # 缺少下划线分隔
x = True             # 变量名不够描述性
max_size = 1000      # 常量应该大写
```

### 变量作用域 | Variable Scope

```python
# 全局变量
global_var = "我是全局变量"

def example_function():
    # 局部变量
    local_var = "我是局部变量"

    # 访问全局变量
    print(f"函数内访问: {global_var}")

    # 修改全局变量需要使用global关键字
    global global_var
    global_var = "修改后的全局变量"

example_function()
print(f"函数外访问: {global_var}")
```

## 类型转换深入 | Advanced Type Conversion

### 安全的类型转换

```python
def safe_int_conversion(value):
    """安全地将值转换为整数"""
    try:
        return int(value)
    except ValueError:
        print(f"无法将 '{value}' 转换为整数")
        return None

# 测试安全转换
test_values = ["123", "45.67", "hello", ""]
for value in test_values:
    result = safe_int_conversion(value)
    print(f"'{value}' -> {result}")

# 类型检查
def process_data(data):
    """根据数据类型进行不同处理"""
    if isinstance(data, int):
        return data * 2
    elif isinstance(data, float):
        return round(data, 2)
    elif isinstance(data, str):
        return data.upper()
    else:
        return f"未知类型: {type(data)}"

# 测试类型检查
test_data = [42, 3.14159, "hello", [1, 2, 3]]
for item in test_data:
    result = process_data(item)
    print(f"{item} ({type(item).__name__}) -> {result}")
```

## 常见错误与最佳实践 | Common Mistakes and Best Practices

### 常见错误

```python
# ❌ 错误1: 变量名拼写错误
name = "Alice"
print(nam)  # NameError: name 'nam' is not defined

# ❌ 错误2: 类型转换错误
number = int("hello")  # ValueError

# ❌ 错误3: 除零错误
result = 10 / 0  # ZeroDivisionError

# ❌ 错误4: 字符串和数字直接相加
age = 25
message = "我今年" + age + "岁"  # TypeError
```

### 最佳实践

```python
# ✅ 使用描述性的变量名
user_age = 25
total_price = 99.99

# ✅ 使用类型提示（Python 3.5+）
def calculate_area(length: float, width: float) -> float:
    """计算矩形面积"""
    return length * width

# ✅ 使用常量
PI = 3.14159
MAX_USERS = 1000

# ✅ 合理使用类型转换
user_input = "25"
if user_input.isdigit():
    age = int(user_input)
    print(f"年龄: {age}")
else:
    print("请输入有效的年龄")
```

## 实践练习 | Practical Exercises

### 练习1: 变量交换
```python
# 任务：不使用临时变量交换两个变量的值
a = 10
b = 20

# 方法1: 使用元组
a, b = b, a

# 方法2: 使用算术运算（仅适用于数字）
# a = a + b
# b = a - b
# a = a - b

print(f"a = {a}, b = {b}")
```

### 练习2: 数据类型检测器
```python
def data_type_detector(value):
    """检测并返回值的详细类型信息"""
    type_name = type(value).__name__

    info = {
        'value': value,
        'type': type_name,
        'is_numeric': isinstance(value, (int, float, complex)),
        'is_sequence': isinstance(value, (str, list, tuple)),
        'is_truthy': bool(value)
    }

    return info

# 测试
test_values = [42, 3.14, "hello", True, [], None, 0]
for val in test_values:
    info = data_type_detector(val)
    print(f"{repr(val)}: {info}")
```

### 练习3: 温度转换器
```python
def temperature_converter():
    """温度转换器"""
    print("温度转换器")
    print("1. 摄氏度转华氏度")
    print("2. 华氏度转摄氏度")

    choice = input("请选择转换类型 (1/2): ")

    try:
        temp = float(input("请输入温度值: "))

        if choice == "1":
            fahrenheit = (temp * 9/5) + 32
            print(f"{temp}°C = {fahrenheit:.2f}°F")
        elif choice == "2":
            celsius = (temp - 32) * 5/9
            print(f"{temp}°F = {celsius:.2f}°C")
        else:
            print("无效的选择")
    except ValueError:
        print("请输入有效的数字")

# 运行转换器
# temperature_converter()
```

## 小结 | Summary

在本章中，我们学习了：

1. **变量的创建和使用**
2. **Python的基本数据类型**：整数、浮点数、字符串、布尔值
3. **类型检查和转换**
4. **变量命名规范和作用域**
5. **常见错误和最佳实践**

### 关键要点
- 变量名应该具有描述性
- 了解不同数据类型的特点和用法
- 掌握安全的类型转换方法
- 遵循PEP 8命名规范

### 下一步
在下一章中，我们将学习Python的运算符，包括算术、比较、逻辑和赋值运算符。