# 5.2 Default Values and `field` (默认值与`field`)

You can provide default values for the fields in a data class. For mutable default values, you should use the `field` function with a `default_factory`.

你可以为数据类中的字段提供默认值。对于可变的默认值，你应该使用带有 `default_factory` 的 `field` 函数。

## Default Values (默认值)

```python
# code/default_values.py

from dataclasses import dataclass

@dataclass
class Card:
    rank: str = 'A'
    suit: str = 'spades'

ace_of_spades = Card()
print(ace_of_spades)

queen_of_hearts = Card('Q', 'hearts')
print(queen_of_hearts)
```

## `field` and `default_factory`

If you want to use a mutable object as a default value, you must use `default_factory` to avoid sharing the same object between different instances of the class.

如果你想使用一个可变对象作为默认值，你必须使用 `default_factory` 来避免在类的不同实例之间共享同一个对象。

```python
# code/field_default_factory.py

from dataclasses import dataclass, field

@dataclass
class ClubMember:
    name: str
    guests: list = field(default_factory=list)

member1 = ClubMember("Alice")
member1.guests.append("Bob")

member2 = ClubMember("Charlie")

print(member1)
print(member2)
```
