# 6.4 Function Arguments and Mutability (函数参数与可变性)

When you pass a mutable object to a function, the function can modify the object. This is because the function receives a reference to the object, not a copy.

当你将一个可变对象传递给一个函数时，该函数可以修改该对象。这是因为函数接收的是对该对象的引用，而不是一个副本。

```python
# code/function_mutability.py

def modify_list(my_list):
    my_list.append(4)

original_list = [1, 2, 3]
print(f"Original list: {original_list}")

modify_list(original_list)
print(f"List after calling modify_list: {original_list}")
```

However, if you reassign the parameter to a new object inside the function, this will not affect the original object.

但是，如果你在函数内部将参数重新分配给一个新对象，这不会影响原始对象。

```python
# code/function_reassignment.py

def reassign_list(my_list):
    my_list = [10, 20, 30]
    print(f"List inside function: {my_list}")

original_list = [1, 2, 3]
print(f"Original list: {original_list}")

reassign_list(original_list)
print(f"List after calling reassign_list: {original_list}")
```
