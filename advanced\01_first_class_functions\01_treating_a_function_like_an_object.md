# 1.1 Treating a Function Like an Object (像对象一样对待函数)

In Python, functions are first-class objects. This means that you can do anything with a function that you can do with any other object, such as an integer, string, or list.

在Python中，函数是一等对象。这意味着你可以对函数执行任何可以对其他对象（如整数、字符串或列表）执行的操作。

## Assigning Functions to Variables (将函数赋给变量)

You can assign a function to a variable, and then use the variable to call the function.

你可以将一个函数赋给一个变量，然后使用该变量来调用该函数。

```python
# code/function_as_object.py

def factorial(n):
    """returns n!"""
    return 1 if n < 2 else n * factorial(n - 1)

fact = factorial
print(f"fact(5): {fact(5)}")
print(f"factorial.__doc__: {factorial.__doc__}")
print(f"type(factorial): {type(factorial)}")
```

## Storing Functions in Data Structures (在数据结构中存储函数)

You can also store functions in data structures, such as lists and dictionaries.

你还可以在数据结构（如列表和字典）中存储函数。

```python
# code/functions_in_data_structures.py

from math import sin, cos, tan

functions = [sin, cos, tan]

for func in functions:
    print(f"{func.__name__}(1): {func(1)}")
```
