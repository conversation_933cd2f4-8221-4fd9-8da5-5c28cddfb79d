# functions_examples.py

# This file demonstrates how to define and use functions in Python.
# 这个文件演示了如何在Python中定义和使用函数。

# A simple function (一个简单的函数)
def greet():
    """This function prints a greeting."""
    print("Hello, World!")

greet()

# A function with a parameter (带参数的函数)
def greet_user(name):
    """This function greets the user by name."""
    print(f"Hello, {name}!")

greet_user("<PERSON>")

# A function with a default parameter value (带默认参数值的函数)
def greet_user_default(name="World"):
    """This function greets the user, with a default name."""
    print(f"Hello, {name}!")

greet_user_default()
greet_user_default("Bob")

# A function that returns a value (返回值的函数)
def square(x):
    """This function returns the square of a number."""
    return x * x

result = square(5)
print(f"The square of 5 is {result}")
