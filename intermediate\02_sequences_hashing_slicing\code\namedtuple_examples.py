# code/namedtuple_examples.py

from collections import namedtuple

# Create a named tuple class (创建一个命名元组类)
City = namedtuple('City', 'name country population coordinates')

# Create an instance of the class (创建类的实例)
tokyo = City('Tokyo', 'JP', 36.933, (35.689722, 139.691667))

# Access fields by name (按名称访问字段)
print(f"City: {tokyo.name}")
print(f"Country: {tokyo.country}")

# Access fields by index (按索引访问字段)
print(f"Population: {tokyo[2]}")
print(f"Coordinates: {tokyo.coordinates}")

# You can also access the fields of the named tuple
# (你也可以访问命名元组的字段)
print(f"Fields: {tokyo._fields}")
