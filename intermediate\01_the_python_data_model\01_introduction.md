# 1.1 Introduction to the Data Model (数据模型简介)

The Python data model is the API you can use to make your own objects play well with the most idiomatic language features. If you have a class that acts as a sequence, you don't have to invent a new syntax for it. You can implement the `__len__` and `__getitem__` methods, and your class will behave like a standard Python sequence, allowing you to use `len()` and square brackets for item access.

Python数据模型是你可以用来让你自己的对象与最地道的语言特性良好协作的API。如果你有一个作为序列的类，你不必为它发明新的语法。你可以实现 `__len__` 和 `__getitem__` 方法，你的类就会像一个标准的Python序列一样，允许你使用 `len()` 和方括号进行项目访问。

This is what we mean by "Pythonic". Writing idiomatic Python code means leveraging the language's features to create code that is clear, concise, and easy to read. The data model is a key part of that.

这就是我们所说的“Pythonic”。编写地道的Python代码意味着利用语言的特性来创建清晰、简洁、易于阅读的代码。数据模型是其中的一个关键部分。

In this chapter, we will explore the data model through a series of examples, starting with a simple card deck.

在本章中，我们将通过一系列的例子来探讨数据模型，从一个简单的扑克牌开始。
