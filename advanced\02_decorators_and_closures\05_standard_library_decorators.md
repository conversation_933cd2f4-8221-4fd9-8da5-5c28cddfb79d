# 2.5 Standard Library Decorators (标准库中的装饰器)

Python's standard library provides several useful decorators.

Python的标准库提供了几个有用的装饰器。

## `@functools.lru_cache`

The `lru_cache` decorator is a memoization decorator that caches the results of a function. This can significantly improve the performance of functions that are called with the same arguments multiple times.

`lru_cache` 装饰器是一个记忆化装饰器，它缓存函数的结果。这可以显著提高使用相同参数多次调用的函数的性能。

```python
# code/lru_cache_example.py

import functools
import time

@functools.lru_cache()
def fibonacci(n):
    if n < 2:
        return n
    return fibonacci(n - 2) + fibon<PERSON>ci(n - 1)

start_time = time.time()
print(fibonacci(30))
end_time = time.time()
print(f"Duration: {end_time - start_time:.4f}s")
```

## `@functools.singledispatch`

The `singledispatch` decorator transforms a function into a single-dispatch generic function. A generic function is a function composed of multiple functions implementing the same operation for different types.

`singledispatch` 装饰器将一个函数转换为一个单分派泛型函数。泛型函数是由为不同类型实现相同操作的多个函数组成的函数。

```python
# code/singledispatch_example.py

from functools import singledispatch

@singledispatch
def fun(arg, verbose=False):
    if verbose:
        print("Let me just say,", end=" ")
    print(arg)

@fun.register
def _(arg: int, verbose=False):
    if verbose:
        print("Strength in numbers, eh?", end=" ")
    print(arg)

@fun.register
def _(arg: list, verbose=False):
    if verbose:
        print("Enumerate this:")
    for i, elem in enumerate(arg):
        print(i, elem)

fun("Hello, world.")
fun("test", verbose=True)
fun(42, verbose=True)
fun(['a', 'b', 'c'], verbose=True)
```
