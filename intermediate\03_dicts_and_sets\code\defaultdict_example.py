# code/defaultdict_example.py

import collections

# Create a defaultdict with a default factory of `int` (which returns 0)
# (创建一个默认工厂为 `int` (返回0) 的 defaultdict)
dd = collections.defaultdict(int)

print(f"dd['a']: {dd['a']}")
print(f"dd after accessing a non-existent key: {dd}")

# You can also use a list as the default factory
# (你也可以使用列表作为默认工厂)
dd_list = collections.defaultdict(list)
dd_list['a'].append(1)
dd_list['a'].append(2)
print(f"dd_list: {dd_list}")
