# 2.1 Decorators 101 (装饰器入门)

A decorator is a callable that takes another function as an argument (the decorated function) and returns a callable. The returned callable is usually a modified version of the decorated function.

装饰器是一个可调用对象，它接受另一个函数作为参数（被装饰的函数）并返回一个可调用对象。返回的可调用对象通常是被装饰函数的一个修改版本。

## A Simple Decorator (一个简单的装饰器)

Here is a simple decorator that prints the arguments and result of a function call.

下面是一个简单的装饰器，它打印函数调用的参数和结果。

```python
# code/simple_decorator.py

def trace(func):
    def wrapper(*args, **kwargs):
        print(f"Calling {func.__name__} with args={args}, kwargs={kwargs}")
        result = func(*args, **kwargs)
        print(f"{func.__name__} returned {result}")
        return result
    return wrapper

@trace
def add(a, b):
    return a + b

add(2, 3)
```

The `@trace` syntax is a shortcut for `add = trace(add)`.

`@trace` 语法是 `add = trace(add)` 的简写。

## The `functools.wraps` Decorator ( `functools.wraps` 装饰器)

When you use a decorator, you are replacing the original function with the wrapper function. This means that the original function's metadata (such as its name and docstring) is lost. To fix this, you can use the `@functools.wraps` decorator.

当你使用装饰器时，你正在用包装函数替换原始函数。这意味着原始函数的元数据（例如其名称和文档字符串）会丢失。为了解决这个问题，你可以使用 `@functools.wraps` 装饰器。

```python
# code/wraps_decorator.py

import functools

def trace(func):
    @functools.wraps(func)
    def wrapper(*args, **kwargs):
        # ...
        return func(*args, **kwargs)
    return wrapper
```
