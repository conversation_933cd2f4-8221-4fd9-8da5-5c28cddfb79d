# Chapter 1: Functions as First-Class Objects (函数作为一等公民)

This chapter explores the concept of functions as first-class objects in Python. This means that functions can be treated like any other object, such as being assigned to variables, passed as arguments to other functions, and returned from other functions. This is a fundamental concept that underpins many of Python's powerful features.

本章探讨了Python中函数作为一等公民的概念。这意味着函数可以像任何其他对象一样被对待，例如被赋值给变量、作为参数传递给其他函数，以及从其他函数返回。这是一个基本概念，是Python许多强大功能的基础。

## Table of Contents (目录)

1.  [Treating a Function Like an Object (像对象一样对待函数)](01_treating_a_function_like_an_object.md)
2.  [Higher-Order Functions (高阶函数)](02_higher_order_functions.md)
3.  [Anonymous Functions (匿名函数)](03_anonymous_functions.md)
4.  [Callable Objects (可调用对象)](04_callable_objects.md)
