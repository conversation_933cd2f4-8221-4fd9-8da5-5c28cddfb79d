# 10. Python内存模型与作用域机制 | Python Memory Model and Scoping Mechanism

深入理解Python的内存模型和作用域机制，是掌握Python高级特性的关键。

Understanding Python's memory model and scoping mechanism is key to mastering Python's advanced features.

## 10.1 Python内存模型概述 | Python Memory Model Overview

Python使用引用计数和垃圾回收来管理内存，同时采用了多种优化策略。

Python uses reference counting and garbage collection to manage memory, along with various optimization strategies.

```python
import sys
import gc

# 查看对象的引用计数
x = [1, 2, 3]
print(f"x的引用计数: {sys.getrefcount(x)}")  # 注意：getrefcount本身会增加一次引用

y = x  # 增加引用
print(f"添加引用后x的引用计数: {sys.getrefcount(x)}")

del y  # 减少引用
print(f"删除引用后x的引用计数: {sys.getrefcount(x)}")

# 查看垃圾回收统计
print(f"垃圾回收统计: {gc.get_stats()}")
```

### 内存布局

```python
# Python对象的内存布局
class MemoryDemo:
    def __init__(self, value):
        self.value = value

obj = MemoryDemo(42)

# 查看对象大小
print(f"对象大小: {sys.getsizeof(obj)} 字节")
print(f"整数大小: {sys.getsizeof(42)} 字节")
print(f"字符串大小: {sys.getsizeof('hello')} 字节")
print(f"列表大小: {sys.getsizeof([1, 2, 3])} 字节")

# 查看对象的内存地址
print(f"对象内存地址: {hex(id(obj))}")
print(f"对象值的内存地址: {hex(id(obj.value))}")
```

## 10.2 作用域和名称空间 | Scope and Namespace

Python使用LEGB规则来解析名称：Local → Enclosing → Global → Built-in

Python uses the LEGB rule to resolve names: Local → Enclosing → Global → Built-in

```python
# 全局作用域
global_var = "我是全局变量"

def outer_function():
    # 封闭作用域 (Enclosing)
    enclosing_var = "我是封闭作用域变量"
    
    def inner_function():
        # 局部作用域 (Local)
        local_var = "我是局部变量"
        
        # LEGB查找顺序演示
        print(f"局部变量: {local_var}")
        print(f"封闭变量: {enclosing_var}")
        print(f"全局变量: {global_var}")
        print(f"内置函数: {len}")  # Built-in
        
        # 查看局部名称空间
        print(f"局部名称空间: {locals()}")
    
    inner_function()
    print(f"外层函数名称空间: {locals()}")

outer_function()
print(f"全局名称空间: {list(globals().keys())[-10:]}")  # 显示最后10个
```

### global和nonlocal关键字

```python
# global关键字
counter = 0  # 全局变量

def increment_global():
    global counter
    counter += 1
    print(f"全局计数器: {counter}")

def increment_local():
    counter = 0  # 局部变量，遮蔽全局变量
    counter += 1
    print(f"局部计数器: {counter}")

print(f"初始全局计数器: {counter}")
increment_global()
increment_local()
print(f"最终全局计数器: {counter}")

# nonlocal关键字
def make_counter():
    count = 0
    
    def increment():
        nonlocal count
        count += 1
        return count
    
    def decrement():
        nonlocal count
        count -= 1
        return count
    
    def get_count():
        return count
    
    return increment, decrement, get_count

inc, dec, get = make_counter()
print(f"初始计数: {get()}")
print(f"递增后: {inc()}")
print(f"递增后: {inc()}")
print(f"递减后: {dec()}")
```

## 10.3 变量生命周期 | Variable Lifecycle

```python
import weakref

class LifecycleDemo:
    def __init__(self, name):
        self.name = name
        print(f"创建对象: {self.name}")
    
    def __del__(self):
        print(f"销毁对象: {self.name}")

def demonstrate_lifecycle():
    # 局部变量的生命周期
    obj = LifecycleDemo("局部对象")
    
    # 创建弱引用来观察对象销毁
    weak_ref = weakref.ref(obj)
    
    def callback(ref):
        print("弱引用回调：对象已被销毁")
    
    weak_ref_with_callback = weakref.ref(obj, callback)
    
    print(f"函数结束前，对象是否存在: {weak_ref() is not None}")
    return weak_ref

# 函数执行完毕，局部变量被销毁
weak_ref = demonstrate_lifecycle()
print(f"函数结束后，对象是否存在: {weak_ref() is not None}")

# 强制垃圾回收
gc.collect()
```

## 10.4 闭包和自由变量 | Closures and Free Variables

```python
def make_multiplier(factor):
    """创建一个乘法器闭包"""
    def multiplier(number):
        return number * factor  # factor是自由变量
    
    # 查看闭包信息
    print(f"闭包的自由变量: {multiplier.__code__.co_freevars}")
    print(f"闭包的单元格: {multiplier.__closure__}")
    
    return multiplier

# 创建不同的乘法器
double = make_multiplier(2)
triple = make_multiplier(3)

print(f"double(5) = {double(5)}")
print(f"triple(5) = {triple(5)}")

# 查看闭包捕获的变量
if double.__closure__:
    for i, cell in enumerate(double.__closure__):
        print(f"闭包变量 {i}: {cell.cell_contents}")

# 闭包变量的延迟绑定问题
def create_functions():
    functions = []
    for i in range(3):
        # 错误的方式：延迟绑定
        functions.append(lambda x: x * i)
    return functions

funcs = create_functions()
for f in funcs:
    print(f"f(10) = {f(10)}")  # 都是20，因为i最终为2

# 正确的方式：立即绑定
def create_functions_correct():
    functions = []
    for i in range(3):
        # 使用默认参数立即绑定
        functions.append(lambda x, multiplier=i: x * multiplier)
    return functions

funcs_correct = create_functions_correct()
for f in funcs_correct:
    print(f"f_correct(10) = {f(10)}")  # 0, 10, 20
```

## 10.5 内存泄漏和循环引用 | Memory Leaks and Circular References

```python
import gc
import weakref

class Node:
    def __init__(self, value):
        self.value = value
        self.children = []
        self.parent = None
    
    def add_child(self, child):
        child.parent = self  # 创建循环引用
        self.children.append(child)
    
    def __del__(self):
        print(f"销毁节点: {self.value}")

# 创建循环引用
def create_circular_reference():
    root = Node("root")
    child1 = Node("child1")
    child2 = Node("child2")
    
    root.add_child(child1)
    root.add_child(child2)
    
    # 创建弱引用来观察
    weak_refs = [weakref.ref(root), weakref.ref(child1), weakref.ref(child2)]
    
    return weak_refs

print("创建循环引用...")
weak_refs = create_circular_reference()

print("检查对象是否存在...")
for i, ref in enumerate(weak_refs):
    print(f"对象 {i} 存在: {ref() is not None}")

print("强制垃圾回收...")
collected = gc.collect()
print(f"回收了 {collected} 个对象")

print("垃圾回收后检查对象...")
for i, ref in enumerate(weak_refs):
    print(f"对象 {i} 存在: {ref() is not None}")

# 使用弱引用避免循环引用
class SafeNode:
    def __init__(self, value):
        self.value = value
        self.children = []
        self._parent = None
    
    @property
    def parent(self):
        return self._parent() if self._parent else None
    
    @parent.setter
    def parent(self, value):
        self._parent = weakref.ref(value) if value else None
    
    def add_child(self, child):
        child.parent = self  # 使用弱引用
        self.children.append(child)
    
    def __del__(self):
        print(f"销毁安全节点: {self.value}")
```

## 10.6 作用域陷阱和最佳实践 | Scope Pitfalls and Best Practices

```python
# 陷阱1: 默认参数的可变对象
def bad_function(items=[]):  # 危险！
    items.append("new item")
    return items

def good_function(items=None):  # 安全
    if items is None:
        items = []
    items.append("new item")
    return items

# 测试默认参数陷阱
print("坏函数调用:")
print(bad_function())  # ['new item']
print(bad_function())  # ['new item', 'new item']

print("好函数调用:")
print(good_function())  # ['new item']
print(good_function())  # ['new item']

# 陷阱2: 循环中的闭包
def create_closures_bad():
    closures = []
    for i in range(3):
        closures.append(lambda: i)  # 延迟绑定
    return closures

def create_closures_good():
    closures = []
    for i in range(3):
        closures.append(lambda x=i: x)  # 立即绑定
    return closures

print("坏闭包:")
bad_closures = create_closures_bad()
for closure in bad_closures:
    print(closure())  # 都是2

print("好闭包:")
good_closures = create_closures_good()
for closure in good_closures:
    print(closure())  # 0, 1, 2

# 陷阱3: 类变量vs实例变量
class BadClass:
    items = []  # 类变量，所有实例共享
    
    def add_item(self, item):
        self.items.append(item)

class GoodClass:
    def __init__(self):
        self.items = []  # 实例变量，每个实例独有
    
    def add_item(self, item):
        self.items.append(item)

# 测试类变量陷阱
obj1 = BadClass()
obj2 = BadClass()
obj1.add_item("item1")
obj2.add_item("item2")
print(f"BadClass obj1.items: {obj1.items}")  # ['item1', 'item2']
print(f"BadClass obj2.items: {obj2.items}")  # ['item1', 'item2']

obj3 = GoodClass()
obj4 = GoodClass()
obj3.add_item("item1")
obj4.add_item("item2")
print(f"GoodClass obj3.items: {obj3.items}")  # ['item1']
print(f"GoodClass obj4.items: {obj4.items}")  # ['item2']
```

## 10.7 内存分析工具 | Memory Analysis Tools

```python
import tracemalloc
import psutil
import os

# 内存跟踪
def memory_analysis_demo():
    # 开始跟踪内存
    tracemalloc.start()
    
    # 创建一些对象
    data = []
    for i in range(1000):
        data.append([i] * 100)
    
    # 获取当前内存快照
    snapshot = tracemalloc.take_snapshot()
    top_stats = snapshot.statistics('lineno')
    
    print("内存使用最多的前3行:")
    for stat in top_stats[:3]:
        print(stat)
    
    # 获取进程内存信息
    process = psutil.Process(os.getpid())
    memory_info = process.memory_info()
    print(f"进程内存使用: {memory_info.rss / 1024 / 1024:.2f} MB")
    
    tracemalloc.stop()

if __name__ == "__main__":
    memory_analysis_demo()
```

## 总结 | Summary

理解Python的内存模型和作用域机制对于编写高效、正确的Python代码至关重要：

1. **内存管理**: 引用计数 + 垃圾回收
2. **作用域规则**: LEGB查找顺序
3. **变量生命周期**: 理解对象的创建和销毁
4. **闭包机制**: 自由变量的捕获和绑定
5. **常见陷阱**: 避免内存泄漏和作用域问题

Understanding Python's memory model and scoping mechanism is crucial for writing efficient and correct Python code:

1. **Memory management**: Reference counting + garbage collection
2. **Scope rules**: LEGB lookup order
3. **Variable lifecycle**: Understanding object creation and destruction
4. **Closure mechanism**: Capturing and binding free variables
5. **Common pitfalls**: Avoiding memory leaks and scope issues
