# 5.4 Inheritance (继承)

Data classes can be inherited from other data classes. This allows you to build on existing data classes and add new fields or methods.

数据类可以从其他数据类继承。这允许你在现有的数据类基础上进行构建，并添加新的字段或方法。

```python
# code/inheritance_dataclass.py

from dataclasses import dataclass

@dataclass
class Person:
    name: str
    age: int

@dataclass
class Employee(Person):
    employee_id: int
    department: str = "Engineering"


person = Person("<PERSON>", 30)
employee = Employee("<PERSON>", 40, 12345)

print(person)
print(employee)
```

In this example, the `Employee` class inherits from the `Person` class. It has all the fields of the `Person` class, plus two new fields: `employee_id` and `department`.

在这个例子中，`Employee` 类继承自 `Person` 类。它拥有 `Person` 类的所有字段，外加两个新字段：`employee_id` 和 `department`。
