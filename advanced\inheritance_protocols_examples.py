# inheritance_protocols_examples.py

# Inheritance (继承)
class Animal:
    def __init__(self, name):
        self.name = name

    def speak(self):
        raise NotImplementedError("Subclass must implement abstract method")

class Dog(Animal):
    def speak(self):
        return f"{self.name} says Woof!"

class Cat(Animal):
    def speak(self):
        return f"{self.name} says Meow!"

print("--- Inheritance ---")
dog = Dog("Fido")
print(dog.speak())
cat = Cat("Whiskers")
print(cat.speak())

# Protocols (协议)
from typing import Protocol

class Speaker(Protocol):
    def speak(self) -> str:
        ...

def make_sound(speaker: Speaker):
    print(speaker.speak())

class Person:
    def __init__(self, name):
        self.name = name

    def speak(self) -> str:
        return f"{self.name} says hello."

print("\n--- Protocols ---")
make_sound(Person("Alice"))
make_sound(dog)
