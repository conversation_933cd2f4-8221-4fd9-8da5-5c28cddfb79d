# 6.2 Identity, Equality, and Aliases (标识、相等性和别名)

In Python, there is a distinction between an object's identity and its value.

在Python中，对象的标识和它的值是有区别的。

*   **Identity (标识):** An object's identity never changes once it has been created; you may think of it as the object’s address in memory. The `is` operator compares the identity of two objects. (一个对象的标识一旦创建就不会改变；你可以把它看作是对象在内存中的地址。`is` 运算符比较两个对象的标识。)
*   **Equality (相等性):** The `==` operator compares the values of two objects. ( `==` 运算符比较两个对象的值。)

## Aliases (别名)

When two variables refer to the same object, they are called aliases.

当两个变量引用同一个对象时，它们被称为别名。

```python
# code/identity_equality_aliases.py

a = [1, 2, 3]
b = a  # b is an alias for a (b是a的别名)

print(f"a == b: {a == b}")  # True, because the values are the same (因为值相同)
print(f"a is b: {a is b}")    # True, because they refer to the same object (因为它们引用同一个对象)

# Create a copy of a (创建a的副本)
c = list(a)

print(f"a == c: {a == c}")  # True, because the values are the same (因为值相同)
print(f"a is c: {a is c}")    # False, because they are different objects (因为它们是不同的对象)
```

Understanding the difference between identity and equality is crucial for avoiding subtle bugs in your code.

理解标识和相等性之间的区别对于避免代码中潜在的错误至关重要。
