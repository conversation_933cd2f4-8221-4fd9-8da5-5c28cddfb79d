# 3.2 Handling Missing Keys (处理缺失的键)

When working with dictionaries, it's common to need to handle cases where a key is not present. There are several ways to do this in Python.

在使用字典时，通常需要处理键不存在的情况。在Python中，有几种方法可以做到这一点。

## `try...except`

The most straightforward way is to use a `try...except` block.

最直接的方法是使用 `try...except` 块。

```python
# code/missing_keys_try.py

my_dict = {'a': 1}

try:
    print(my_dict['b'])
except KeyError:
    print("Key 'b' not found.")
```

## `in` operator

You can also use the `in` operator to check if a key exists before trying to access it.

你也可以在使用 `in` 运算符在尝试访问键之前检查它是否存在。

```python
# code/missing_keys_in.py

my_dict = {'a': 1}

if 'b' in my_dict:
    print(my_dict['b'])
else:
    print("Key 'b' not found.")
```

## `collections.defaultdict`

The `collections.defaultdict` class is a subclass of `dict` that provides a default value for a nonexistent key. You provide the default value factory as an argument to the constructor.

`collections.defaultdict` 类是 `dict` 的一个子类，它为一个不存在的键提供一个默认值。你将默认值工厂作为构造函数的参数提供。

```python
# code/defaultdict_example.py

import collections

# Create a defaultdict with a default factory of `int` (which returns 0)
# (创建一个默认工厂为 `int` (返回0) 的 defaultdict)
dd = collections.defaultdict(int)

print(f"dd['a']: {dd['a']}")
print(f"dd after accessing a non-existent key: {dd}")
```
