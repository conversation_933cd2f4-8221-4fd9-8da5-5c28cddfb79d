# control_flow_examples.py

# This file demonstrates control flow statements in Python.
# 这个文件演示了Python中的控制流语句。

# if, elif, else
print("--- if, elif, else ---")
x = 10
if x > 10:
    print("x is greater than 10")
elif x < 10:
    print("x is less than 10")
else:
    print("x is equal to 10")

# for loop
print("\n--- for loop ---")
for i in range(5):
    print(i, end=" ")
print()

# while loop
print("\n--- while loop ---")
i = 0
while i < 5:
    print(i, end=" ")
    i += 1
print()

# break and continue
print("\n--- break and continue ---")
print("break example:")
for i in range(10):
    if i == 5:
        break
    print(i, end=" ")
print()

print("continue example:")
for i in range(10):
    if i % 2 == 0:
        continue
    print(i, end=" ")
print()

