# 1.6 Overview of Special Methods (特殊方法概述)

The Python data model provides a large number of special methods. Here are some of the most common ones, grouped by category:

Python数据模型提供了大量的特殊方法。以下是一些最常见的，按类别分组：

### Emulating container types (模拟容器类型)

*   `__len__(self)`: Called by `len()`.
*   `__getitem__(self, key)`: Called to implement evaluation of `self[key]`.
*   `__setitem__(self, key, value)`: Called to implement assignment to `self[key]`.
*   `__delitem__(self, key)`: Called to implement deletion of `self[key]`.
*   `__iter__(self)`: Called when an iterator for the container is requested.
*   `__contains__(self, item)`: Called to implement membership test operators.

### Emulating numeric types (模拟数字类型)

*   `__add__(self, other)`: `+`
*   `__sub__(self, other)`: `-`
*   `__mul__(self, other)`: `*`
*   `__truediv__(self, other)`: `/`
*   `__floordiv__(self, other)`: `//`
*   `__mod__(self, other)`: `%`
*   `__pow__(self, other[, modulo])`: `**`
*   `__abs__(self)`: `abs()`
*   `__bool__(self)`: `bool()`

### String representation (字符串表示)

*   `__repr__(self)`: Called by `repr()`.
*   `__str__(self)`: Called by `str()`.
*   `__format__(self, format_spec)`: Called by `format()`.

This is just a small sample of the special methods available in Python. For a complete list, see the [Python documentation](https://docs.python.org/3/reference/datamodel.html#special-method-names).

这只是Python中可用的特殊方法的一小部分。有关完整列表，请参阅[Python文档](https://docs.python.org/3/reference/datamodel.html#special-method-names)。
