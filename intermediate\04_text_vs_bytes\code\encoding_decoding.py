# code/encoding_decoding.py

# Encoding a string to bytes (将字符串编码为字节)
s = "café"
print(f"Original string: {s}")

utf8_bytes = s.encode('utf-8')
print(f"UTF-8 encoded: {utf8_bytes}")

# Decoding bytes to a string (将字节解码为字符串)
decoded_s = utf8_bytes.decode('utf-8')
print(f"Decoded from UTF-8: {decoded_s}")

# Trying to decode with the wrong encoding will raise an error
# (尝试使用错误的编码解码会引发错误)
try:
    latin1_decoded = utf8_bytes.decode('latin-1')
except UnicodeDecodeError as e:
    print(f"\nError decoding with latin-1: {e}")

