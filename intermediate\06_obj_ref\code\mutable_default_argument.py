# code/mutable_default_argument.py

class HauntedBus:
    """A bus model haunted by ghost passengers"""

    def __init__(self, passengers=[]):
        self.passengers = passengers

    def pick(self, name):
        self.passengers.append(name)

    def drop(self, name):
        self.passengers.remove(name)

bus1 = HauntedBus(['Alice', '<PERSON>'])
bus1.pick('Charlie')
print(f"bus1.passengers: {bus1.passengers}")

# The default passengers list is shared between all instances
# (默认的乘客列表在所有实例之间共享)
bus2 = HauntedBus()
bus2.pick('Carrie')
print(f"bus2.passengers: {bus2.passengers}")

bus3 = HauntedBus()
print(f"bus3.passengers: {bus3.passengers}")

bus3.pick('Dave')
print(f"bus2.passengers after bus3.pick('Dave'): {bus2.passengers}")
