# 1.5 Boolean Value of a Custom Type (自定义类型的布尔值)

By default, instances of user-defined classes are considered truthy, unless `__bool__` or `__len__` is implemented.

默认情况下，用户定义类的实例被认为是真值，除非实现了 `__bool__` 或 `__len__`。

*   If `__bool__` is implemented, its return value (either `True` or `False`) determines the truthiness of the object. (如果实现了 `__bool__`，它的返回值（`True` 或 `False`）决定了对象的真假。)
*   If `__bool__` is not implemented, but `__len__` is, then the object is considered truthy if the result of `len(obj)` is non-zero. (如果没有实现 `__bool__`，但实现了 `__len__`，那么如果 `len(obj)` 的结果非零，则该对象被认为是真值。)

In our `Vector` class, we implemented `__bool__` like this:

在我们的 `Vector` 类中，我们这样实现 `__bool__`：

```python
    def __bool__(self):
        return bool(abs(self))
```

This means that a `Vector` object is considered false if its magnitude is zero, and true otherwise.

这意味着如果一个 `Vector` 对象的模为零，则它被认为是假的，否则为真。

```python
>>> from vector2d_v2 import Vector
>>> bool(Vector(0, 0))  # Magnitude is 0
False
>>> bool(Vector(1, 1))  # Magnitude is not 0
True
```
