#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Python内存模型与作用域机制示例
Python Memory Model and Scoping Mechanism Examples

演示Python的内存管理、作用域规则、闭包机制等核心概念。
Demonstrates Python's memory management, scope rules, closure mechanisms, and other core concepts.
"""

import sys
import gc
import weakref
import tracemalloc
from functools import wraps


# 全局变量
global_counter = 0
global_list = []


def demonstrate_legb_scope():
    """演示LEGB作用域规则"""
    print("=== LEGB作用域规则演示 ===")
    
    # Built-in scope
    print(f"Built-in函数len: {len}")
    
    # Global scope
    global_var = "全局变量"
    
    def outer_function():
        # Enclosing scope
        enclosing_var = "封闭作用域变量"
        
        def inner_function():
            # Local scope
            local_var = "局部变量"
            
            print(f"  Local: {local_var}")
            print(f"  Enclosing: {enclosing_var}")
            print(f"  Global: {global_var}")
            print(f"  Built-in len: {len([1, 2, 3])}")
            
            # 查看名称空间
            print(f"  局部名称空间: {list(locals().keys())}")
        
        inner_function()
        print(f"  外层函数名称空间: {list(locals().keys())}")
    
    outer_function()


def demonstrate_global_nonlocal():
    """演示global和nonlocal关键字"""
    print("\n=== global和nonlocal演示 ===")
    
    global global_counter
    
    def modify_global():
        global global_counter
        global_counter += 1
        print(f"  修改全局计数器: {global_counter}")
    
    def create_counter():
        count = 0
        
        def increment():
            nonlocal count
            count += 1
            return count
        
        def get_count():
            return count
        
        return increment, get_count
    
    print(f"初始全局计数器: {global_counter}")
    modify_global()
    modify_global()
    
    inc, get = create_counter()
    print(f"局部计数器初始值: {get()}")
    print(f"递增后: {inc()}")
    print(f"再次递增后: {inc()}")


def demonstrate_closure():
    """演示闭包机制"""
    print("\n=== 闭包机制演示 ===")
    
    def make_multiplier(factor):
        """创建乘法器闭包"""
        def multiplier(number):
            return number * factor  # factor是自由变量
        
        # 查看闭包信息
        print(f"  自由变量: {multiplier.__code__.co_freevars}")
        if multiplier.__closure__:
            print(f"  闭包单元格: {[cell.cell_contents for cell in multiplier.__closure__]}")
        
        return multiplier
    
    # 创建不同的乘法器
    double = make_multiplier(2)
    triple = make_multiplier(3)
    
    print(f"  double(5) = {double(5)}")
    print(f"  triple(5) = {triple(5)}")
    
    # 演示闭包的延迟绑定问题
    def create_functions_wrong():
        """错误的闭包创建方式"""
        functions = []
        for i in range(3):
            functions.append(lambda x: x * i)  # 延迟绑定
        return functions
    
    def create_functions_correct():
        """正确的闭包创建方式"""
        functions = []
        for i in range(3):
            functions.append(lambda x, multiplier=i: x * multiplier)  # 立即绑定
        return functions
    
    print("\n  延迟绑定问题:")
    wrong_funcs = create_functions_wrong()
    for i, f in enumerate(wrong_funcs):
        print(f"    wrong_func[{i}](10) = {f(10)}")
    
    print("  正确的立即绑定:")
    correct_funcs = create_functions_correct()
    for i, f in enumerate(correct_funcs):
        print(f"    correct_func[{i}](10) = {f(10)}")


def demonstrate_memory_management():
    """演示内存管理"""
    print("\n=== 内存管理演示 ===")
    
    class MemoryDemo:
        def __init__(self, name):
            self.name = name
            print(f"    创建对象: {self.name}")
        
        def __del__(self):
            print(f"    销毁对象: {self.name}")
    
    # 引用计数
    obj = MemoryDemo("测试对象")
    print(f"  引用计数: {sys.getrefcount(obj)}")
    
    ref1 = obj
    print(f"  添加引用后: {sys.getrefcount(obj)}")
    
    del ref1
    print(f"  删除引用后: {sys.getrefcount(obj)}")
    
    # 弱引用
    weak_ref = weakref.ref(obj)
    print(f"  弱引用存在: {weak_ref() is not None}")
    
    del obj
    print(f"  删除强引用后弱引用存在: {weak_ref() is not None}")
    
    # 垃圾回收
    collected = gc.collect()
    print(f"  垃圾回收了 {collected} 个对象")


def demonstrate_circular_reference():
    """演示循环引用处理"""
    print("\n=== 循环引用处理演示 ===")
    
    class Node:
        def __init__(self, value):
            self.value = value
            self.children = []
            self.parent = None
        
        def add_child(self, child):
            child.parent = self
            self.children.append(child)
        
        def __del__(self):
            print(f"    销毁节点: {self.value}")
    
    def create_cycle():
        root = Node("root")
        child1 = Node("child1")
        child2 = Node("child2")
        
        root.add_child(child1)
        root.add_child(child2)
        
        return [weakref.ref(root), weakref.ref(child1), weakref.ref(child2)]
    
    print("  创建循环引用...")
    weak_refs = create_cycle()
    
    print("  检查对象存在性:")
    for i, ref in enumerate(weak_refs):
        print(f"    对象{i}: {ref() is not None}")
    
    print("  强制垃圾回收...")
    collected = gc.collect()
    print(f"    回收了 {collected} 个对象")
    
    print("  垃圾回收后检查:")
    for i, ref in enumerate(weak_refs):
        print(f"    对象{i}: {ref() is not None}")


def demonstrate_scope_pitfalls():
    """演示作用域陷阱"""
    print("\n=== 作用域陷阱演示 ===")
    
    # 陷阱1: 默认参数的可变对象
    def bad_function(items=[]):
        items.append("new")
        return items
    
    def good_function(items=None):
        if items is None:
            items = []
        items.append("new")
        return items
    
    print("  默认参数陷阱:")
    print(f"    bad_function(): {bad_function()}")
    print(f"    bad_function(): {bad_function()}")  # 累积效应
    print(f"    good_function(): {good_function()}")
    print(f"    good_function(): {good_function()}")  # 每次都是新的
    
    # 陷阱2: 类变量vs实例变量
    class BadClass:
        items = []  # 类变量，所有实例共享
        
        def add_item(self, item):
            self.items.append(item)
    
    class GoodClass:
        def __init__(self):
            self.items = []  # 实例变量
        
        def add_item(self, item):
            self.items.append(item)
    
    print("\n  类变量陷阱:")
    bad1, bad2 = BadClass(), BadClass()
    bad1.add_item("item1")
    bad2.add_item("item2")
    print(f"    bad1.items: {bad1.items}")
    print(f"    bad2.items: {bad2.items}")  # 共享同一个列表
    
    good1, good2 = GoodClass(), GoodClass()
    good1.add_item("item1")
    good2.add_item("item2")
    print(f"    good1.items: {good1.items}")
    print(f"    good2.items: {good2.items}")  # 各自独立


def memory_profiling_demo():
    """内存分析演示"""
    print("\n=== 内存分析演示 ===")
    
    # 开始内存跟踪
    tracemalloc.start()
    
    # 创建一些数据
    data = []
    for i in range(1000):
        data.append([i] * 10)
    
    # 获取内存快照
    snapshot = tracemalloc.take_snapshot()
    top_stats = snapshot.statistics('lineno')
    
    print("  内存使用最多的前3行:")
    for i, stat in enumerate(top_stats[:3]):
        print(f"    {i+1}. {stat}")
    
    # 清理数据
    del data
    gc.collect()
    
    tracemalloc.stop()


def demonstrate_variable_lifetime():
    """演示变量生命周期"""
    print("\n=== 变量生命周期演示 ===")
    
    class LifetimeDemo:
        def __init__(self, name):
            self.name = name
            print(f"    创建: {self.name}")
        
        def __del__(self):
            print(f"    销毁: {self.name}")
    
    def local_scope():
        local_obj = LifetimeDemo("局部对象")
        return weakref.ref(local_obj)
    
    print("  进入局部作用域...")
    weak_ref = local_scope()
    print(f"  退出局部作用域后，对象存在: {weak_ref() is not None}")
    
    # 全局作用域对象
    global_obj = LifetimeDemo("全局对象")
    global_weak_ref = weakref.ref(global_obj)
    
    del global_obj
    print(f"  删除全局对象后，对象存在: {global_weak_ref() is not None}")


def demonstrate_namespace_manipulation():
    """演示名称空间操作"""
    print("\n=== 名称空间操作演示 ===")
    
    def show_namespaces():
        local_var = "局部变量"
        
        print("  局部名称空间:")
        for name, value in locals().items():
            print(f"    {name}: {value}")
        
        print("  全局名称空间(部分):")
        global_names = list(globals().keys())
        for name in global_names[-5:]:  # 显示最后5个
            print(f"    {name}: {type(globals()[name])}")
    
    show_namespaces()
    
    # 动态修改名称空间
    def dynamic_namespace():
        # 动态添加变量
        locals()['dynamic_var'] = "动态变量"
        # 注意：locals()的修改在函数中不会生效
        
        # 使用exec在局部作用域执行代码
        exec("exec_var = '通过exec创建的变量'")
        print(f"  通过exec创建的变量: {locals().get('exec_var', '不存在')}")
    
    dynamic_namespace()


def decorator_with_closure(func):
    """使用闭包的装饰器示例"""
    call_count = 0
    
    @wraps(func)
    def wrapper(*args, **kwargs):
        nonlocal call_count
        call_count += 1
        print(f"  函数 {func.__name__} 被调用第 {call_count} 次")
        return func(*args, **kwargs)
    
    wrapper.call_count = lambda: call_count
    return wrapper


@decorator_with_closure
def example_function(x):
    """示例函数"""
    return x * 2


def demonstrate_decorator_closure():
    """演示装饰器中的闭包"""
    print("\n=== 装饰器闭包演示 ===")
    
    result1 = example_function(5)
    result2 = example_function(10)
    
    print(f"  调用次数: {example_function.call_count()}")
    print(f"  结果: {result1}, {result2}")


def main():
    """主函数"""
    print("Python内存模型与作用域机制演示")
    print("=" * 50)
    
    demonstrate_legb_scope()
    demonstrate_global_nonlocal()
    demonstrate_closure()
    demonstrate_memory_management()
    demonstrate_circular_reference()
    demonstrate_scope_pitfalls()
    memory_profiling_demo()
    demonstrate_variable_lifetime()
    demonstrate_namespace_manipulation()
    demonstrate_decorator_closure()
    
    print("\n" + "=" * 50)
    print("演示完成！")


if __name__ == "__main__":
    main()
