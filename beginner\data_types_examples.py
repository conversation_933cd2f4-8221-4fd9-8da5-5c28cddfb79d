# data_types_examples.py

# This file demonstrates the basic data types in Python.
# 这个文件演示了Python中的基本数据类型。

# Integer (整数)
x = 10
print(f"x = {x}, type of x is {type(x)}")

# Float (浮点数)
y = 3.14
print(f"y = {y}, type of y is {type(y)}")

# String (字符串)
z = "hello"
print(f"z = {z}, type of z is {type(z)}")

# Boolean (布尔值)
a = True
print(f"a = {a}, type of a is {type(a)}")

# Type Casting (类型转换)
float_x = float(x)
print(f"float(x) = {float_x}, type of float_x is {type(float_x)}")

int_y = int(y)
print(f"int(y) = {int_y}, type of int_y is {type(int_y)}")

str_x = str(x)
print(f"str(x) = {str_x}, type of str_x is {type(str_x)}")
