# dataclasses_examples.py

from dataclasses import dataclass, field

@dataclass
class Card:
    rank: str
    suit: str

queen_of_hearts = Card('Q', 'hearts')
print(f"Card: {queen_of_hearts}")
print(f"Rank: {queen_of_hearts.rank}")
print(f"Suit: {queen_of_hearts.suit}")
print(f"queen_of_hearts == Card('Q', 'hearts'): {queen_of_hearts == Card('Q', 'hearts')}")

@dataclass
class ClubMember:
    name: str
    guests: list = field(default_factory=list)

member1 = ClubMember("Alice")
member1.guests.append("Bob")
member2 = ClubMember("Charlie")
print(f"Member 1: {member1}")
print(f"Member 2: {member2}")

@dataclass(frozen=True)
class ImmutableCard:
    rank: str
    suit: str

card = ImmutableCard('A', 'spades')
try:
    card.rank = 'K'
except Exception as e:
    print(f"\nError trying to modify a frozen data class: {e}")
