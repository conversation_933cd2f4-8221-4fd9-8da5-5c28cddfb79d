# 7. 文件操作与异常处理 | File Operations and Exception Handling

在实际编程中，我们经常需要读取和写入文件，同时处理可能出现的错误。本章将学习如何在Python中进行文件操作和异常处理。

In real programming, we often need to read and write files while handling potential errors. This chapter will teach you how to perform file operations and exception handling in Python.

## 文件操作基础 | File Operations Basics

### 打开和关闭文件 | Opening and Closing Files

```python
# 基本的文件操作
# Basic file operations

# 打开文件
file = open('example.txt', 'r', encoding='utf-8')

# 读取内容
content = file.read()
print(content)

# 关闭文件（重要！）
file.close()
```

### 使用 with 语句 | Using with Statement

`with` 语句是处理文件的推荐方式，它会自动关闭文件：

```python
# 推荐的文件操作方式
with open('example.txt', 'r', encoding='utf-8') as file:
    content = file.read()
    print(content)
# 文件会自动关闭
```

### 文件模式 | File Modes

| 模式 | 描述 | Description |
|------|------|-------------|
| 'r' | 只读模式（默认） | Read only (default) |
| 'w' | 写入模式（覆盖） | Write (overwrite) |
| 'a' | 追加模式 | Append |
| 'x' | 创建模式 | Create (fail if exists) |
| 'b' | 二进制模式 | Binary mode |
| 't' | 文本模式（默认） | Text mode (default) |
| '+' | 读写模式 | Read and write |

```python
# 不同的文件模式示例
# Examples of different file modes

# 读取文件
with open('data.txt', 'r', encoding='utf-8') as file:
    content = file.read()

# 写入文件（覆盖）
with open('output.txt', 'w', encoding='utf-8') as file:
    file.write('Hello, World!')

# 追加到文件
with open('log.txt', 'a', encoding='utf-8') as file:
    file.write('新的日志条目\n')

# 二进制模式
with open('image.jpg', 'rb') as file:
    binary_data = file.read()
```

## 读取文件的不同方法 | Different Ways to Read Files

### read() - 读取整个文件

```python
with open('example.txt', 'r', encoding='utf-8') as file:
    # 读取整个文件
    content = file.read()
    print(content)
    
    # 读取指定字符数
    file.seek(0)  # 回到文件开头
    first_10_chars = file.read(10)
    print(f"前10个字符: {first_10_chars}")
```

### readline() - 逐行读取

```python
with open('example.txt', 'r', encoding='utf-8') as file:
    # 读取第一行
    first_line = file.readline()
    print(f"第一行: {first_line.strip()}")
    
    # 读取第二行
    second_line = file.readline()
    print(f"第二行: {second_line.strip()}")
```

### readlines() - 读取所有行到列表

```python
with open('example.txt', 'r', encoding='utf-8') as file:
    lines = file.readlines()
    for i, line in enumerate(lines, 1):
        print(f"第{i}行: {line.strip()}")
```

### 迭代文件对象 | Iterating Over File Objects

```python
# 最高效的逐行读取方式
with open('example.txt', 'r', encoding='utf-8') as file:
    for line_number, line in enumerate(file, 1):
        print(f"第{line_number}行: {line.strip()}")
```

## 写入文件 | Writing to Files

### write() 和 writelines()

```python
# 写入单个字符串
with open('output.txt', 'w', encoding='utf-8') as file:
    file.write('Hello, World!\n')
    file.write('这是第二行\n')

# 写入多行
lines = ['第一行\n', '第二行\n', '第三行\n']
with open('output.txt', 'w', encoding='utf-8') as file:
    file.writelines(lines)

# 使用 print() 写入文件
with open('output.txt', 'w', encoding='utf-8') as file:
    print('Hello, World!', file=file)
    print('这是第二行', file=file)
```

## 异常处理基础 | Exception Handling Basics

### try-except 语句

```python
# 基本的异常处理
try:
    # 可能出错的代码
    number = int(input("请输入一个数字: "))
    result = 10 / number
    print(f"结果: {result}")
except ValueError:
    print("输入的不是有效数字！")
except ZeroDivisionError:
    print("不能除以零！")
```

### 捕获多种异常

```python
try:
    # 可能出现多种错误的代码
    filename = input("请输入文件名: ")
    with open(filename, 'r') as file:
        content = file.read()
        number = int(content)
        result = 100 / number
        print(f"结果: {result}")
        
except FileNotFoundError:
    print("文件不存在！")
except ValueError:
    print("文件内容不是有效数字！")
except ZeroDivisionError:
    print("文件中的数字不能为零！")
except Exception as e:
    print(f"发生了未知错误: {e}")
```

### else 和 finally 子句

```python
try:
    file = open('data.txt', 'r')
    data = file.read()
    print("文件读取成功")
except FileNotFoundError:
    print("文件不存在")
else:
    # 只有在没有异常时才执行
    print("处理数据...")
    processed_data = data.upper()
finally:
    # 无论是否有异常都会执行
    print("清理资源...")
    if 'file' in locals() and not file.closed:
        file.close()
```

## 常见的文件操作异常 | Common File Operation Exceptions

```python
import os

def safe_file_operations():
    """演示安全的文件操作"""
    
    # 检查文件是否存在
    filename = 'example.txt'
    
    if os.path.exists(filename):
        print(f"文件 {filename} 存在")
        
        # 检查文件权限
        if os.access(filename, os.R_OK):
            print("文件可读")
        if os.access(filename, os.W_OK):
            print("文件可写")
    else:
        print(f"文件 {filename} 不存在")
        
        # 创建文件
        try:
            with open(filename, 'w') as file:
                file.write("这是一个新文件\n")
            print("文件创建成功")
        except PermissionError:
            print("没有权限创建文件")
        except OSError as e:
            print(f"操作系统错误: {e}")

safe_file_operations()
```

## 实际应用示例 | Practical Examples

### 日志记录器 | Logger

```python
import datetime

class SimpleLogger:
    """简单的日志记录器"""
    
    def __init__(self, filename='app.log'):
        self.filename = filename
    
    def log(self, message, level='INFO'):
        """记录日志消息"""
        timestamp = datetime.datetime.now().strftime('%Y-%m-%d %H:%M:%S')
        log_entry = f"[{timestamp}] {level}: {message}\n"
        
        try:
            with open(self.filename, 'a', encoding='utf-8') as file:
                file.write(log_entry)
        except Exception as e:
            print(f"日志记录失败: {e}")
    
    def info(self, message):
        self.log(message, 'INFO')
    
    def error(self, message):
        self.log(message, 'ERROR')
    
    def warning(self, message):
        self.log(message, 'WARNING')

# 使用示例
logger = SimpleLogger()
logger.info("应用程序启动")
logger.warning("这是一个警告")
logger.error("发生了错误")
```

### 配置文件读取器 | Configuration File Reader

```python
def read_config(filename='config.txt'):
    """读取配置文件"""
    config = {}
    
    try:
        with open(filename, 'r', encoding='utf-8') as file:
            for line_number, line in enumerate(file, 1):
                line = line.strip()
                
                # 跳过空行和注释
                if not line or line.startswith('#'):
                    continue
                
                # 解析键值对
                if '=' in line:
                    key, value = line.split('=', 1)
                    config[key.strip()] = value.strip()
                else:
                    print(f"警告: 第{line_number}行格式不正确: {line}")
                    
    except FileNotFoundError:
        print(f"配置文件 {filename} 不存在，使用默认配置")
        config = {
            'host': 'localhost',
            'port': '8080',
            'debug': 'False'
        }
    except Exception as e:
        print(f"读取配置文件时出错: {e}")
        return None
    
    return config

# 使用示例
config = read_config()
if config:
    print("配置信息:")
    for key, value in config.items():
        print(f"  {key}: {value}")
```

## 最佳实践 | Best Practices

### 1. 始终使用 with 语句

```python
# ✅ 好的做法
with open('file.txt', 'r') as file:
    content = file.read()

# ❌ 不好的做法
file = open('file.txt', 'r')
content = file.read()
file.close()  # 可能忘记关闭或在异常时不执行
```

### 2. 指定编码

```python
# ✅ 明确指定编码
with open('file.txt', 'r', encoding='utf-8') as file:
    content = file.read()

# ❌ 依赖系统默认编码
with open('file.txt', 'r') as file:
    content = file.read()
```

### 3. 处理大文件

```python
def process_large_file(filename):
    """处理大文件的高效方法"""
    try:
        with open(filename, 'r', encoding='utf-8') as file:
            for line in file:  # 逐行处理，不会占用大量内存
                # 处理每一行
                processed_line = line.strip().upper()
                print(processed_line)
    except FileNotFoundError:
        print(f"文件 {filename} 不存在")
    except Exception as e:
        print(f"处理文件时出错: {e}")
```

### 4. 创建备份

```python
import shutil
import os

def safe_write_file(filename, content):
    """安全地写入文件（创建备份）"""
    backup_filename = filename + '.backup'
    
    try:
        # 如果原文件存在，创建备份
        if os.path.exists(filename):
            shutil.copy2(filename, backup_filename)
        
        # 写入新内容
        with open(filename, 'w', encoding='utf-8') as file:
            file.write(content)
        
        # 删除备份文件
        if os.path.exists(backup_filename):
            os.remove(backup_filename)
            
        print("文件写入成功")
        
    except Exception as e:
        print(f"写入文件失败: {e}")
        
        # 恢复备份
        if os.path.exists(backup_filename):
            shutil.move(backup_filename, filename)
            print("已恢复备份文件")
```

## 小结 | Summary

在本章中，我们学习了：

1. **文件操作基础**：打开、读取、写入和关闭文件
2. **文件模式**：不同的文件访问模式
3. **异常处理**：try-except-else-finally 语句
4. **常见异常**：FileNotFoundError、PermissionError等
5. **最佳实践**：使用with语句、指定编码、处理大文件等

### 关键要点
- 使用 `with` 语句确保文件正确关闭
- 明确指定文件编码
- 适当处理可能的异常
- 对于大文件，使用逐行处理

### 下一步
在下一章中，我们将学习Python的模块和包系统，了解如何组织和重用代码。
