# dict_set_examples.py

# Dictionary examples (字典示例)
print("--- Dictionaries ---")
person = {"name": "<PERSON>", "age": 30, "city": "New York"}
for key, value in person.items():
    print(f"{key}: {value}")

# Dictionary comprehension (字典推导)
dial_codes = [
    (880, 'Bangladesh'),
    (55,  'Brazil'),
    (86,  'China'),
]
country_dial = {country: code for code, country in dial_codes}
print(f"Country dial codes: {country_dial}")

# Set examples (集合示例)
print("\n--- Sets ---")
my_set = {1, 2, 3, 4, 5, 5, 5}
print(f"Original set: {my_set}")

set1 = {1, 2, 3}
set2 = {3, 4, 5}
print(f"set1: {set1}")
print(f"set2: {set2}")
print(f"Union: {set1 | set2}")
print(f"Intersection: {set1 & set2}")
print(f"Difference: {set1 - set2}")
print(f"Symmetric Difference: {set1 ^ set2}")

