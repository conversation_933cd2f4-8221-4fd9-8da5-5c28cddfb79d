# 4.1 Inheritance and Subclassing (继承与子类化)

Inheritance is a mechanism for creating a new class that is a specialized version of an existing class. The new class, called a subclass, inherits the attributes and methods of the existing class, called the superclass.

继承是创建现有类的一个专门版本的新类的机制。新类，称为子类，继承现有类（称为超类）的属性和方法。

## A Simple Example (一个简单的例子)

```python
# code/simple_inheritance.py

class Animal:
    def __init__(self, name):
        self.name = name

    def speak(self):
        return f"{self.name} makes a sound."

class Dog(Animal):
    def speak(self):
        return f"{self.name} barks."

dog = Dog("Fido")
print(dog.speak())
```

In this example, the `Dog` class inherits from the `Animal` class. The `Dog` class overrides the `speak` method to provide its own implementation.

在这个例子中，`Dog` 类继承自 `Animal` 类。`Dog` 类重写了 `speak` 方法以提供自己的实现。

## The `super()` Function ( `super()` 函数)

The `super()` function can be used to call a method from the superclass.

`super()` 函数可用于调用超类中的方法。

```python
# code/super_example.py

class Animal:
    def __init__(self, name):
        self.name = name

class Dog(Animal):
    def __init__(self, name, breed):
        super().__init__(name)
        self.breed = breed

dog = Dog("Fido", "Golden Retriever")
print(f"Name: {dog.name}, Breed: {dog.breed}")
```
