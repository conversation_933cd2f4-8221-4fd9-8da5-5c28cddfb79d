# operators_examples.py

# This file demonstrates the use of various operators in Python.
# 这个文件演示了Python中各种运算符的用法。

# Arithmetic Operators (算术运算符)
x = 10
y = 3

print("--- Arithmetic Operators ---")
print(f"{x} + {y} = {x + y}")
print(f"{x} - {y} = {x - y}")
print(f"{x} * {y} = {x * y}")
print(f"{x} / {y} = {x / y}")
print(f"{x} % {y} = {x % y}")
print(f"{x} ** {y} = {x ** y}")
print(f"{x} // {y} = {x // y}")

# Comparison Operators (比较运算符)
a = 5
b = 3

print("\n--- Comparison Operators ---")
print(f"{a} == {b} is {a == b}")
print(f"{a} != {b} is {a != b}")
print(f"{a} > {b} is {a > b}")
print(f"{a} < {b} is {a < b}")
print(f"{a} >= {b} is {a >= b}")
print(f"{a} <= {b} is {a <= b}")

# Logical Operators (逻辑运算符)
p = True
q = False

print("\n--- Logical Operators ---")
print(f"{p} and {q} is {p and q}")
print(f"{p} or {q} is {p or q}")
print(f"not {p} is {not p}")
