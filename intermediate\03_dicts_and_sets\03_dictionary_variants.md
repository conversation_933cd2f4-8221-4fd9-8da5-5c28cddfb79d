# 3.3 Dictionary Variants in `collections` (`collections`中的字典变体)

The `collections` module provides several specialized dictionary-like container datatypes.

`collections` 模块提供了几种专门的类似字典的容器数据类型。

## `collections.ChainMap`

A `ChainMap` class is provided for quickly linking a number of mappings so that they can be treated as a single unit. It is often much faster than creating a new dictionary and running multiple `update()` calls.

提供了一个 `ChainMap` 类，用于快速链接多个映射，以便可以将它们视为一个单元。它通常比创建一个新字典并运行多个 `update()` 调用要快得多。

```python
# code/chainmap_example.py

from collections import ChainMap

d1 = {'a': 1, 'b': 2}
d2 = {'b': 3, 'c': 4}

chain = ChainMap(d1, d2)

print(f"chain['a']: {chain['a']}")
print(f"chain['b']: {chain['b']}")
print(f"chain['c']: {chain['c']}")
```

## `collections.Counter`

A `Counter` is a `dict` subclass for counting hashable objects. It is an unordered collection where elements are stored as dictionary keys and their counts are stored as dictionary values.

`Counter` 是一个 `dict` 子类，用于对可哈希对象进行计数。它是一个无序的集合，其中元素作为字典键存储，它们的计数作为字典值存储。

```python
# code/counter_example.py

from collections import Counter

my_list = ['a', 'b', 'c', 'a', 'b', 'a']
counts = Counter(my_list)

print(f"Counts: {counts}")
print(f"Most common: {counts.most_common(1)}")
```
