# concurrency_examples.py

import threading
import multiprocessing
import asyncio
import time

# Multithreading (多线程)
def thread_worker(name):
    print(f"Thread {name}: starting")
    time.sleep(2)
    print(f"Thread {name}: finishing")

print("--- Multithreading ---")
threads = []
for i in range(2):
    t = threading.Thread(target=thread_worker, args=(i,))
    threads.append(t)
    t.start()

for t in threads:
    t.join()

# Multiprocessing (多进程)
def process_worker(name):
    print(f"Process {name}: starting")
    time.sleep(2)
    print(f"Process {name}: finishing")

if __name__ == "__main__":
    print("\n--- Multiprocessing ---")
    processes = []
    for i in range(2):
        p = multiprocessing.Process(target=process_worker, args=(i,))
        processes.append(p)
        p.start()

    for p in processes:
        p.join()

# Asynchronous I/O (异步IO)
async def async_main():
    print('\n--- Asynchronous I/O ---')
    print('hello')
    await asyncio.sleep(1)
    print('world')

if __name__ == "__main__":
    asyncio.run(async_main())
