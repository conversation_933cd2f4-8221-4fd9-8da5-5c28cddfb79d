# code/higher_order_functions.py

from functools import reduce

# map
numbers = [1, 2, 3, 4, 5]
squares = map(lambda x: x**2, numbers)
print(f"Squares: {list(squares)}")

# filter
evens = filter(lambda x: x % 2 == 0, numbers)
print(f"Evens: {list(evens)}")

# reduce
product = reduce(lambda x, y: x * y, numbers)
print(f"Product: {product}")

# Using list comprehensions and generator expressions
# (使用列表推导和生成器表达式)
print("\n--- Using comprehensions and genexps ---")
squares_lc = [x**2 for x in numbers]
print(f"Squares (list comprehension): {squares_lc}")
evens_ge = (x for x in numbers if x % 2 == 0)
print(f"Evens (generator expression): {list(evens_ge)}")
