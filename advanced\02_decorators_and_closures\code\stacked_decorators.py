# code/stacked_decorators.py

def bold(func):
    def wrapper():
        return "<b>" + func() + "</b>"
    return wrapper

def italic(func):
    def wrapper():
        return "<i>" + func() + "</i>"
    return wrapper

@bold
@italic
def greet():
    return "Hello, World!"

print(f"greet(): {greet()}")

# This is equivalent to:
# (这等同于：)
def greet_equivalent():
    return "Hello, World!"

greet_equivalent = bold(italic(greet_equivalent))
print(f"greet_equivalent(): {greet_equivalent()}")
