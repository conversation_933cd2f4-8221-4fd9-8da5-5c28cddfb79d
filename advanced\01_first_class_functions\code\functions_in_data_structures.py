# code/functions_in_data_structures.py

from math import sin, cos, tan

functions = [sin, cos, tan]

for func in functions:
    print(f"{func.__name__}(1): {func(1)}")

# You can also store functions in a dictionary
# (你也可以在字典中存储函数)
func_dict = {
    'sine': sin,
    'cosine': cos,
    'tangent': tan
}

print("\n--- Functions in a dictionary ---")
for name, func in func_dict.items():
    print(f"{name}(1): {func(1)}")

