# 1.4 String Representation: `__repr__` vs `__str__` (字符串表示)

In Python, there are two special methods for creating string representations of your objects: `__repr__` and `__str__`.

在Python中，有两个特殊方法可以为你的对象创建字符串表示：`__repr__` 和 `__str__`。

*   `__repr__`: The `__repr__` method should return an unambiguous representation of the object, which should ideally be a string of Python code that can be used to recreate the object. ( `__repr__` 方法应该返回一个明确的对象表示，理想情况下，它应该是一个可以用来重新创建该对象的Python代码字符串。)
*   `__str__`: The `__str__` method should return a user-friendly string representation of the object. ( `__str__` 方法应该返回一个用户友好的对象字符串表示。)

If you only implement one of these, `__repr__` is the one to choose, because `__str__` will default to calling `__repr__`.

如果你只实现其中一个，那么应该选择 `__repr__`，因为 `__str__` 会默认调用 `__repr__`。

Let's add a `__str__` method to our `Vector` class:

让我们为我们的 `Vector` 类添加一个 `__str__` 方法：

```python
# code/vector2d_v2.py

import math

class Vector:

    def __init__(self, x=0, y=0):
        self.x = x
        self.y = y

    def __repr__(self):
        return f'Vector({self.x!r}, {self.y!r})'

    def __str__(self):
        return f'({self.x}, {self.y})'

    # ... other methods
```

Now, when we print a `Vector` object, we get a more user-friendly output:

现在，当我们打印一个 `Vector` 对象时，我们会得到一个更用户友好的输出：

```python
>>> from vector2d_v2 import Vector
>>> v = Vector(3, 4)
>>> print(v)
(3, 4)
>>> repr(v)
'Vector(3, 4)'
```
