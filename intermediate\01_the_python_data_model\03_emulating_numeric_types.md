# 1.3 Emulating Numeric Types (模拟数字类型)

Just as we can make our objects behave like sequences, we can also make them behave like numbers. We do this by implementing the special methods for numeric operations.

就像我们可以让我们的对象表现得像序列一样，我们也可以让它们表现得像数字。我们通过实现数字操作的特殊方法来做到这一点。

Here's a simple 2D vector class:

这是一个简单的二维向量类：

```python
# code/vector2d.py

import math

class Vector:

    def __init__(self, x=0, y=0):
        self.x = x
        self.y = y

    def __repr__(self):
        return f'Vector({self.x!r}, {self.y!r})'

    def __abs__(self):
        return math.hypot(self.x, self.y)

    def __bool__(self):
        return bool(abs(self))

    def __add__(self, other):
        x = self.x + other.x
        y = self.y + other.y
        return Vector(x, y)

    def __mul__(self, scalar):
        return Vector(self.x * scalar, self.y * scalar)
```

By implementing `__add__` and `__mul__`, we can use the `+` and `*` operators with our `Vector` objects:

通过实现 `__add__` 和 `__mul__`，我们可以对我们的 `Vector` 对象使用 `+` 和 `*` 运算符：

```python
>>> from vector2d import Vector
>>> v1 = Vector(2, 4)
>>> v2 = Vector(2, 1)
>>> v1 + v2
Vector(4, 5)
>>> v = Vector(3, 4)
>>> abs(v)
5.0
>>> v * 3
Vector(9, 12)
```

By implementing `__abs__`, we can use the `abs()` function on our `Vector` objects. And by implementing `__bool__`, we can use our `Vector` objects in boolean contexts.

通过实现 `__abs__`，我们可以在我们的 `Vector` 对象上使用 `abs()` 函数。通过实现 `__bool__`，我们可以在布尔上下文中使用我们的 `Vector` 对象。
