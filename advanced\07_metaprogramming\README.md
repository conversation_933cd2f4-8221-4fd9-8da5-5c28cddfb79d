# Chapter 7: Metaprogramming and Reflection (元编程与反射)

元编程是编写能够操作程序本身的程序的技术。在Python中，元编程允许你在运行时检查、修改和创建代码。这是一个强大但需要谨慎使用的特性。

Metaprogramming is the technique of writing programs that manipulate programs themselves. In Python, metaprogramming allows you to inspect, modify, and create code at runtime. This is a powerful feature that should be used judiciously.

## Table of Contents (目录)

1. [Introduction to Metaprogramming (元编程简介)](01_introduction.md)
2. [Introspection and Reflection (内省与反射)](02_introspection_reflection.md)
3. [Dynamic Attribute Access (动态属性访问)](03_dynamic_attributes.md)
4. [Metaclasses (元类)](04_metaclasses.md)
5. [Class Decorators (类装饰器)](05_class_decorators.md)
6. [Dynamic Class Creation (动态类创建)](06_dynamic_class_creation.md)
7. [Code Generation (代码生成)](07_code_generation.md)
8. [Advanced Metaprogramming Patterns (高级元编程模式)](08_advanced_patterns.md)

## Learning Objectives (学习目标)

完成本章学习后，你将能够：

After completing this chapter, you will be able to:

- 理解Python的内省和反射机制
- 使用动态属性访问技术
- 掌握元类的概念和应用
- 创建和使用类装饰器
- 动态创建类和函数
- 生成和执行代码
- 应用元编程解决复杂的设计问题

- Understand Python's introspection and reflection mechanisms
- Use dynamic attribute access techniques
- Master the concept and application of metaclasses
- Create and use class decorators
- Dynamically create classes and functions
- Generate and execute code
- Apply metaprogramming to solve complex design problems

## Key Concepts (核心概念)

### Introspection (内省)

内省是程序在运行时检查自身结构的能力：

Introspection is the ability of a program to examine its own structure at runtime:

```python
import inspect

def example_function(x, y=10):
    """示例函数"""
    return x + y

# 检查函数信息
print(f"函数名: {example_function.__name__}")
print(f"文档字符串: {example_function.__doc__}")
print(f"参数签名: {inspect.signature(example_function)}")
print(f"源代码: {inspect.getsource(example_function)}")
```

### Reflection (反射)

反射是程序在运行时修改自身结构的能力：

Reflection is the ability of a program to modify its own structure at runtime:

```python
class DynamicClass:
    pass

# 动态添加属性
setattr(DynamicClass, 'new_attribute', 'value')

# 动态添加方法
def new_method(self):
    return "动态添加的方法"

setattr(DynamicClass, 'new_method', new_method)

# 使用动态添加的属性和方法
obj = DynamicClass()
print(obj.new_attribute)  # 'value'
print(obj.new_method())   # '动态添加的方法'
```

### Metaclasses (元类)

元类是创建类的类，控制类的创建过程：

Metaclasses are classes that create classes, controlling the class creation process:

```python
class SingletonMeta(type):
    """单例模式元类"""
    _instances = {}
    
    def __call__(cls, *args, **kwargs):
        if cls not in cls._instances:
            cls._instances[cls] = super().__call__(*args, **kwargs)
        return cls._instances[cls]

class Singleton(metaclass=SingletonMeta):
    def __init__(self, value):
        self.value = value

# 测试单例模式
s1 = Singleton("first")
s2 = Singleton("second")
print(s1 is s2)  # True
print(s1.value)  # "first"
```

## Why Use Metaprogramming? (为什么使用元编程？)

### 1. 框架开发 (Framework Development)

```python
# ORM框架示例
class ModelMeta(type):
    def __new__(mcs, name, bases, namespace):
        # 自动创建数据库字段映射
        fields = {}
        for key, value in namespace.items():
            if isinstance(value, Field):
                fields[key] = value
                value.name = key
        
        namespace['_fields'] = fields
        return super().__new__(mcs, name, bases, namespace)

class Field:
    def __init__(self, field_type, default=None):
        self.field_type = field_type
        self.default = default
        self.name = None

class Model(metaclass=ModelMeta):
    pass

class User(Model):
    name = Field(str)
    age = Field(int, default=0)
    email = Field(str)

# 自动生成的字段映射
print(User._fields)  # {'name': <Field>, 'age': <Field>, 'email': <Field>}
```

### 2. API包装器 (API Wrappers)

```python
class APIWrapper:
    def __init__(self, base_url):
        self.base_url = base_url
    
    def __getattr__(self, name):
        def api_call(*args, **kwargs):
            url = f"{self.base_url}/{name}"
            # 实际的API调用逻辑
            return f"调用 {url} 参数: {args}, {kwargs}"
        return api_call

# 动态API调用
api = APIWrapper("https://api.example.com")
result = api.get_users(limit=10)  # 调用 https://api.example.com/get_users
```

### 3. 配置和验证 (Configuration and Validation)

```python
class ValidatedMeta(type):
    def __new__(mcs, name, bases, namespace):
        # 添加验证逻辑
        original_init = namespace.get('__init__', lambda self: None)
        
        def validated_init(self, *args, **kwargs):
            original_init(self, *args, **kwargs)
            self._validate()
        
        namespace['__init__'] = validated_init
        return super().__new__(mcs, name, bases, namespace)

class ValidatedClass(metaclass=ValidatedMeta):
    def _validate(self):
        # 验证逻辑
        for attr_name, attr_value in self.__dict__.items():
            if hasattr(self.__class__, f'_validate_{attr_name}'):
                validator = getattr(self.__class__, f'_validate_{attr_name}')
                validator(self, attr_value)

class Person(ValidatedClass):
    def __init__(self, name, age):
        self.name = name
        self.age = age
    
    def _validate_age(self, age):
        if not isinstance(age, int) or age < 0:
            raise ValueError("年龄必须是非负整数")
    
    def _validate_name(self, name):
        if not isinstance(name, str) or len(name) == 0:
            raise ValueError("姓名必须是非空字符串")
```

## Common Metaprogramming Techniques (常见元编程技术)

### 1. 动态属性访问 (Dynamic Attribute Access)

```python
class DynamicObject:
    def __init__(self, **kwargs):
        self._data = kwargs
    
    def __getattr__(self, name):
        if name in self._data:
            return self._data[name]
        raise AttributeError(f"'{self.__class__.__name__}' 没有属性 '{name}'")
    
    def __setattr__(self, name, value):
        if name.startswith('_'):
            super().__setattr__(name, value)
        else:
            if not hasattr(self, '_data'):
                super().__setattr__('_data', {})
            self._data[name] = value
    
    def __delattr__(self, name):
        if name in self._data:
            del self._data[name]
        else:
            raise AttributeError(f"'{self.__class__.__name__}' 没有属性 '{name}'")

# 使用动态对象
obj = DynamicObject(name="Alice", age=30)
print(obj.name)  # "Alice"
obj.city = "New York"  # 动态添加属性
print(obj.city)  # "New York"
```

### 2. 属性描述符 (Property Descriptors)

```python
class TypedProperty:
    def __init__(self, name, expected_type):
        self.name = name
        self.expected_type = expected_type
    
    def __get__(self, obj, objtype=None):
        if obj is None:
            return self
        return obj.__dict__.get(self.name)
    
    def __set__(self, obj, value):
        if not isinstance(value, self.expected_type):
            raise TypeError(f"{self.name} 必须是 {self.expected_type.__name__} 类型")
        obj.__dict__[self.name] = value
    
    def __delete__(self, obj):
        del obj.__dict__[self.name]

class TypedClass:
    name = TypedProperty('name', str)
    age = TypedProperty('age', int)
    
    def __init__(self, name, age):
        self.name = name
        self.age = age

# 类型检查
person = TypedClass("Alice", 30)
# person.age = "thirty"  # 会抛出TypeError
```

### 3. 类装饰器 (Class Decorators)

```python
def add_repr(cls):
    """为类添加__repr__方法的装饰器"""
    def __repr__(self):
        attrs = ', '.join(f'{k}={v!r}' for k, v in self.__dict__.items())
        return f'{cls.__name__}({attrs})'
    
    cls.__repr__ = __repr__
    return cls

def add_equality(cls):
    """为类添加相等性比较的装饰器"""
    def __eq__(self, other):
        if not isinstance(other, cls):
            return False
        return self.__dict__ == other.__dict__
    
    def __hash__(self):
        return hash(tuple(sorted(self.__dict__.items())))
    
    cls.__eq__ = __eq__
    cls.__hash__ = __hash__
    return cls

@add_repr
@add_equality
class Point:
    def __init__(self, x, y):
        self.x = x
        self.y = y

# 自动生成的方法
p1 = Point(1, 2)
p2 = Point(1, 2)
print(p1)  # Point(x=1, y=2)
print(p1 == p2)  # True
```

## Advanced Patterns (高级模式)

### 1. 注册模式 (Registry Pattern)

```python
class RegistryMeta(type):
    registry = {}
    
    def __new__(mcs, name, bases, namespace):
        cls = super().__new__(mcs, name, bases, namespace)
        if hasattr(cls, 'register_name'):
            mcs.registry[cls.register_name] = cls
        return cls
    
    @classmethod
    def get_class(mcs, name):
        return mcs.registry.get(name)

class Plugin(metaclass=RegistryMeta):
    pass

class DatabasePlugin(Plugin):
    register_name = 'database'

class CachePlugin(Plugin):
    register_name = 'cache'

# 通过名称获取类
db_class = RegistryMeta.get_class('database')
cache_class = RegistryMeta.get_class('cache')
```

### 2. 代理模式 (Proxy Pattern)

```python
class LazyProxy:
    def __init__(self, target_class, *args, **kwargs):
        self._target_class = target_class
        self._target_args = args
        self._target_kwargs = kwargs
        self._target = None
    
    def _get_target(self):
        if self._target is None:
            self._target = self._target_class(*self._target_args, **self._target_kwargs)
        return self._target
    
    def __getattr__(self, name):
        return getattr(self._get_target(), name)
    
    def __setattr__(self, name, value):
        if name.startswith('_'):
            super().__setattr__(name, value)
        else:
            setattr(self._get_target(), name, value)

class ExpensiveResource:
    def __init__(self, name):
        print(f"创建昂贵资源: {name}")
        self.name = name
    
    def do_work(self):
        return f"{self.name} 正在工作"

# 延迟加载
proxy = LazyProxy(ExpensiveResource, "重要资源")
print("代理已创建")  # 此时还没有创建实际对象
print(proxy.do_work())  # 现在才创建实际对象
```

## Best Practices (最佳实践)

1. **谨慎使用**: 元编程很强大，但也会增加代码复杂性
2. **文档化**: 为元编程代码提供详细的文档
3. **测试**: 确保元编程代码有充分的测试覆盖
4. **性能考虑**: 注意元编程可能带来的性能开销
5. **可读性**: 保持代码的可读性和可维护性

1. **Use judiciously**: Metaprogramming is powerful but increases code complexity
2. **Document**: Provide detailed documentation for metaprogramming code
3. **Test**: Ensure metaprogramming code has adequate test coverage
4. **Performance**: Consider potential performance overhead of metaprogramming
5. **Readability**: Maintain code readability and maintainability

## Common Pitfalls (常见陷阱)

- 过度使用元编程导致代码难以理解
- 忽略性能影响
- 缺乏适当的错误处理
- 破坏代码的可预测性
- 调试困难

- Overusing metaprogramming leading to hard-to-understand code
- Ignoring performance implications
- Lack of proper error handling
- Breaking code predictability
- Debugging difficulties

让我们开始深入学习Python元编程的各个方面！

Let's start diving deep into various aspects of Python metaprogramming!
