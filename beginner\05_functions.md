# 5. Functions (函数)

A function is a block of code that only runs when it is called. You can pass data, known as parameters, into a function. A function can return data as a result.

函数是一个只有在被调用时才会运行的代码块。你可以将数据（称为参数）传递给函数。函数可以返回数据作为结果。

## Creating a Function (创建函数)

In Python, a function is defined using the `def` keyword:

在Python中，函数使用 `def` 关键字定义：

```python
def my_function():
  print("Hello from a function")
```

## Calling a Function (调用函数)

To call a function, use the function name followed by parenthesis:

要调用一个函数，请使用函数名后跟括号：

```python
my_function()
```

## Arguments (参数)

Information can be passed into functions as arguments. Arguments are specified after the function name, inside the parentheses. You can add as many arguments as you want, just separate them with a comma.

信息可以作为参数传递给函数。参数在函数名后的括号内指定。你可以添加任意数量的参数，只需用逗号分隔即可。

```python
def greet(name):
  print("Hello, " + name + "!")

greet("Alice")
greet("Bob")
```

### Default Parameter Value (默认参数值)

You can define a default value for a parameter. If you call the function without an argument, it will use the default value.

你可以为参数定义一个默认值。如果你调用函数时没有提供参数，它将使用默认值。

```python
def greet(name="World"):
  print("Hello, " + name + "!")

greet()  # Hello, World!
greet("Alice")  # Hello, Alice!
```

## Return Values (返回值)

To let a function return a value, use the `return` statement:

要让函数返回值，请使用 `return` 语句：

```python
def square(x):
  return x * x

result = square(5)
print(result)  # 25
```

## Docstrings (文档字符串)

It's a good practice to add a docstring to your functions to explain what they do.

为你的函数添加文档字符串来解释它的功能是一个好习惯。

```python
def square(x):
  """This function returns the square of a number."""
  return x * x

print(square.__doc__)
```