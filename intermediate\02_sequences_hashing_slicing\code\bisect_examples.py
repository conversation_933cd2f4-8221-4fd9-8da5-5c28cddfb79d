# code/bisect_examples.py

import bisect

HAYSTACK = [1, 4, 5, 6, 8, 12, 15, 20, 21, 23, 23, 26, 29, 30]
NEEDLES = [0, 1, 2, 5, 8, 10, 22, 23, 29, 30, 31]

ROW_FMT = '{0:2d} @ {1:2d}    {2}{0:<2d}'

def demo(bisect_fn):
    for needle in reversed(NEEDLES):
        position = bisect_fn(HAYSTACK, needle)
        offset = position * '  |'
        print(ROW_FMT.format(needle, position, offset))

if __name__ == '__main__':
    print('--- bisect ---')
    demo(bisect.bisect)
    print('\n--- bisect_left ---')
    demo(bisect.bisect_left)

    print("\n--- insort ---")
    my_list = []
    for i in [5, 2, 8, 1, 9]:
        bisect.insort(my_list, i)
        print(f"insort {i}: {my_list}")

