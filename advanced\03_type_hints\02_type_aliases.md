# 3.2 Type Aliases (类型别名)

A type alias is a name that refers to a previously defined type. Type aliases can be used to create shorter, more readable names for complex types.

类型别名是引用先前定义的类型的名称。类型别名可用于为复杂类型创建更短、更易读的名称。

## Creating Type Aliases (创建类型别名)

You can create a type alias by assigning a type to a variable.

你可以通过将类型赋给变量来创建类型别名。

```python
# code/type_aliases.py

from typing import List, Dict, Tuple

# A complex type hint (一个复杂的类型提示)
Vector = List[float]

# Another complex type hint (另一个复杂的类型提示)
Headers = Dict[str, str]

# A type hint for a tuple of a string and an integer
# (一个字符串和整数元组的类型提示)
Person = Tuple[str, int]

def scale(scalar: float, vector: Vector) -> Vector:
    return [scalar * num for num in vector]

def greet(name: str, headers: Headers) -> None:
    print(f"Hello, {name}!")
    for key, value in headers.items():
        print(f"{key}: {value}")


person: Person = ("Alice", 30)
```

Using type aliases can make your code easier to read and understand, especially when you are working with complex data structures.

使用类型别名可以使你的代码更容易阅读和理解，尤其是在处理复杂数据结构时。
