# first_class_functions_examples.py

# Assigning Functions to Variables (把函数赋值给变量)
def greet(name):
    return f"Hello, {name}!"

hello = greet
print(hello("<PERSON>"))

# Passing Functions as Arguments (把函数作为参数传递)
def apply(func, x):
    return func(x)

def square(x):
    return x * x

print(apply(square, 5))

# Returning Functions (返回函数)
def make_adder(n):
    def adder(x):
        return x + n
    return adder

add_5 = make_adder(5)
print(add_5(10))
