#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Python专家级教程综合演示
Comprehensive Python Expert Tutorial Demo

这个文件展示了我们教程中涵盖的各种高级Python概念和技术。
This file demonstrates various advanced Python concepts and techniques covered in our tutorial.
"""

import asyncio
import time
import sys
import gc
import weakref
import functools
import threading
from contextlib import contextmanager
from typing import Any, Dict, List, Optional, Union
from dataclasses import dataclass
from abc import ABC, abstractmethod


# ============================================================================
# 1. Python对象模型演示 (Python Object Model Demo)
# ============================================================================

class ObjectModelDemo:
    """演示Python对象模型的核心概念"""
    
    def __init__(self, name: str):
        self.name = name
        print(f"创建对象: {self.name} (ID: {id(self)})")
    
    def __del__(self):
        print(f"销毁对象: {self.name}")
    
    def demonstrate_identity_and_equality(self):
        """演示对象身份和相等性"""
        # 身份比较
        obj1 = ObjectModelDemo("obj1")
        obj2 = ObjectModelDemo("obj2")
        obj3 = obj1
        
        print(f"obj1 is obj2: {obj1 is obj2}")  # False
        print(f"obj1 is obj3: {obj1 is obj3}")  # True
        print(f"obj1 == obj3: {obj1 == obj3}")  # True (默认使用is比较)


# ============================================================================
# 2. 内存管理与作用域演示 (Memory Management and Scoping Demo)
# ============================================================================

def demonstrate_closure():
    """演示闭包机制"""
    def make_counter(start=0):
        count = start
        
        def counter():
            nonlocal count
            count += 1
            return count
        
        return counter
    
    # 创建两个独立的计数器
    counter1 = make_counter(0)
    counter2 = make_counter(100)
    
    print(f"Counter1: {counter1()}, {counter1()}")  # 1, 2
    print(f"Counter2: {counter2()}, {counter2()}")  # 101, 102


def demonstrate_memory_optimization():
    """演示内存优化技术"""
    
    # 使用__slots__优化内存
    class OptimizedClass:
        __slots__ = ['x', 'y']
        
        def __init__(self, x, y):
            self.x = x
            self.y = y
    
    # 普通类
    class RegularClass:
        def __init__(self, x, y):
            self.x = x
            self.y = y
    
    # 内存使用比较
    optimized = OptimizedClass(1, 2)
    regular = RegularClass(1, 2)
    
    print(f"优化类大小: {sys.getsizeof(optimized)} bytes")
    print(f"普通类大小: {sys.getsizeof(regular)} bytes")


# ============================================================================
# 3. 上下文管理器演示 (Context Manager Demo)
# ============================================================================

class DatabaseConnection:
    """数据库连接上下文管理器示例"""
    
    def __init__(self, host: str, port: int):
        self.host = host
        self.port = port
        self.connection = None
    
    def __enter__(self):
        print(f"连接到数据库 {self.host}:{self.port}")
        self.connection = f"connection_to_{self.host}_{self.port}"
        return self.connection
    
    def __exit__(self, exc_type, exc_value, traceback):
        print("关闭数据库连接")
        self.connection = None
        if exc_type:
            print(f"处理异常: {exc_type.__name__}: {exc_value}")
        return False

@contextmanager
def timer_context(name: str):
    """计时器上下文管理器"""
    start = time.time()
    print(f"开始 {name}")
    try:
        yield
    finally:
        end = time.time()
        print(f"{name} 耗时: {end - start:.4f}s")


# ============================================================================
# 4. 迭代器与生成器演示 (Iterator and Generator Demo)
# ============================================================================

class FibonacciIterator:
    """斐波那契数列迭代器"""
    
    def __init__(self, max_count: int):
        self.max_count = max_count
        self.count = 0
        self.a, self.b = 0, 1
    
    def __iter__(self):
        return self
    
    def __next__(self):
        if self.count >= self.max_count:
            raise StopIteration
        
        if self.count == 0:
            self.count += 1
            return self.a
        elif self.count == 1:
            self.count += 1
            return self.b
        else:
            self.a, self.b = self.b, self.a + self.b
            self.count += 1
            return self.b

def fibonacci_generator(max_count: int):
    """斐波那契数列生成器"""
    a, b = 0, 1
    count = 0
    
    while count < max_count:
        if count == 0:
            yield a
        elif count == 1:
            yield b
        else:
            a, b = b, a + b
            yield b
        count += 1

def demonstrate_iterators_generators():
    """演示迭代器和生成器"""
    print("=== 迭代器演示 ===")
    fib_iter = FibonacciIterator(10)
    for num in fib_iter:
        print(num, end=" ")
    print()
    
    print("=== 生成器演示 ===")
    fib_gen = fibonacci_generator(10)
    for num in fib_gen:
        print(num, end=" ")
    print()


# ============================================================================
# 5. 元编程演示 (Metaprogramming Demo)
# ============================================================================

class SingletonMeta(type):
    """单例模式元类"""
    _instances = {}
    
    def __call__(cls, *args, **kwargs):
        if cls not in cls._instances:
            cls._instances[cls] = super().__call__(*args, **kwargs)
        return cls._instances[cls]

class Singleton(metaclass=SingletonMeta):
    """使用元类的单例类"""
    
    def __init__(self, value: str):
        if not hasattr(self, 'initialized'):
            self.value = value
            self.initialized = True

def class_decorator(cls):
    """类装饰器示例"""
    original_init = cls.__init__
    
    def new_init(self, *args, **kwargs):
        print(f"创建 {cls.__name__} 实例")
        original_init(self, *args, **kwargs)
    
    cls.__init__ = new_init
    return cls

@class_decorator
class DecoratedClass:
    def __init__(self, name: str):
        self.name = name


# ============================================================================
# 6. 性能优化演示 (Performance Optimization Demo)
# ============================================================================

@functools.lru_cache(maxsize=128)
def cached_fibonacci(n: int) -> int:
    """使用缓存的斐波那契函数"""
    if n < 2:
        return n
    return cached_fibonacci(n-1) + cached_fibonacci(n-2)

def uncached_fibonacci(n: int) -> int:
    """未使用缓存的斐波那契函数"""
    if n < 2:
        return n
    return uncached_fibonacci(n-1) + uncached_fibonacci(n-2)

def performance_comparison():
    """性能比较演示"""
    n = 30
    
    # 测试缓存版本
    start = time.time()
    result1 = cached_fibonacci(n)
    cached_time = time.time() - start
    
    # 测试非缓存版本
    start = time.time()
    result2 = uncached_fibonacci(n)
    uncached_time = time.time() - start
    
    print(f"缓存版本: {cached_time:.6f}s, 结果: {result1}")
    print(f"非缓存版本: {uncached_time:.6f}s, 结果: {result2}")
    print(f"性能提升: {uncached_time / cached_time:.2f}x")


# ============================================================================
# 7. 异步编程演示 (Async Programming Demo)
# ============================================================================

async def async_task(name: str, delay: float) -> str:
    """异步任务示例"""
    print(f"任务 {name} 开始")
    await asyncio.sleep(delay)
    print(f"任务 {name} 完成")
    return f"任务 {name} 的结果"

async def async_context_manager_demo():
    """异步上下文管理器演示"""
    
    class AsyncResource:
        async def __aenter__(self):
            print("获取异步资源")
            await asyncio.sleep(0.1)
            return self
        
        async def __aexit__(self, exc_type, exc_val, exc_tb):
            print("释放异步资源")
            await asyncio.sleep(0.1)
    
    async with AsyncResource():
        print("使用异步资源")
        await asyncio.sleep(0.5)

async def demonstrate_async_programming():
    """演示异步编程"""
    print("=== 异步编程演示 ===")
    
    # 并发执行多个任务
    tasks = [
        async_task("A", 1.0),
        async_task("B", 0.5),
        async_task("C", 1.5)
    ]
    
    start = time.time()
    results = await asyncio.gather(*tasks)
    end = time.time()
    
    print(f"所有任务完成，耗时: {end - start:.2f}s")
    print(f"结果: {results}")
    
    # 异步上下文管理器
    await async_context_manager_demo()


# ============================================================================
# 8. 综合示例：数据处理管道 (Comprehensive Example: Data Processing Pipeline)
# ============================================================================

@dataclass
class DataItem:
    """数据项"""
    id: int
    value: str
    timestamp: float

class DataProcessor:
    """数据处理器"""
    
    def __init__(self):
        self.processed_count = 0
    
    def process_item(self, item: DataItem) -> DataItem:
        """处理单个数据项"""
        self.processed_count += 1
        processed_value = item.value.upper()
        return DataItem(item.id, processed_value, time.time())
    
    def process_batch(self, items: List[DataItem]) -> List[DataItem]:
        """批处理数据项"""
        return [self.process_item(item) for item in items]

def data_pipeline_demo():
    """数据处理管道演示"""
    print("=== 数据处理管道演示 ===")
    
    # 创建测试数据
    test_data = [
        DataItem(i, f"item_{i}", time.time())
        for i in range(10)
    ]
    
    # 使用上下文管理器和计时器
    with timer_context("数据处理"):
        processor = DataProcessor()
        
        # 批处理数据
        processed_data = processor.process_batch(test_data)
        
        print(f"处理了 {processor.processed_count} 个数据项")
        print(f"前3个处理结果: {processed_data[:3]}")


# ============================================================================
# 主函数 (Main Function)
# ============================================================================

def main():
    """主函数，运行所有演示"""
    print("Python专家级教程综合演示")
    print("=" * 60)
    
    # 1. 对象模型演示
    print("\n1. 对象模型演示")
    demo = ObjectModelDemo("demo")
    demo.demonstrate_identity_and_equality()
    
    # 2. 闭包演示
    print("\n2. 闭包演示")
    demonstrate_closure()
    
    # 3. 内存优化演示
    print("\n3. 内存优化演示")
    demonstrate_memory_optimization()
    
    # 4. 上下文管理器演示
    print("\n4. 上下文管理器演示")
    with DatabaseConnection("localhost", 5432) as conn:
        print(f"使用连接: {conn}")
    
    # 5. 迭代器和生成器演示
    print("\n5. 迭代器和生成器演示")
    demonstrate_iterators_generators()
    
    # 6. 元编程演示
    print("\n6. 元编程演示")
    s1 = Singleton("first")
    s2 = Singleton("second")
    print(f"单例测试: s1 is s2 = {s1 is s2}, s1.value = {s1.value}")
    
    decorated = DecoratedClass("test")
    
    # 7. 性能优化演示
    print("\n7. 性能优化演示")
    performance_comparison()
    
    # 8. 数据处理管道演示
    data_pipeline_demo()
    
    print("\n" + "=" * 60)
    print("所有演示完成！")

async def async_main():
    """异步主函数"""
    print("\n8. 异步编程演示")
    await demonstrate_async_programming()

if __name__ == "__main__":
    # 运行同步演示
    main()
    
    # 运行异步演示
    asyncio.run(async_main())
