# Chapter 4: Inheritance and Protocols (继承与协议)

This chapter explores two fundamental concepts in object-oriented programming: inheritance and protocols. We will see how to use inheritance to create specialized subclasses, and how to use protocols to define interfaces that can be implemented by any class.

本章探讨了面向对象编程中的两个基本概念：继承和协议。我们将看到如何使用继承来创建专门的子类，以及如何使用协议来定义可以由任何类实现的接口。

## Table of Contents (目录)

1.  [Inheritance and Subclassing (继承与子类化)](01_inheritance_and_subclassing.md)
2.  [Multiple Inheritance (多重继承)](02_multiple_inheritance.md)
3.  [Protocols and Duck Typing (协议与鸭子类型)](03_protocols_and_duck_typing.md)
4.  [Abstract Base Classes (ABCs) (抽象基类)](04_abstract_base_classes.md)
