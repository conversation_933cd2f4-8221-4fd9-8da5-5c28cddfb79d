# Chapter 5: Data Classes (数据类)

This chapter introduces data classes, a feature added in Python 3.7 that provides a decorator and functions for automatically adding special methods such as `__init__()` and `__repr__()` to user-defined classes.

本章介绍数据类，这是Python 3.7中添加的一个功能，它提供了一个装饰器和一些函数，用于自动向用户定义的类中添加特殊方法，例如 `__init__()` 和 `__repr__()`。

## Table of Contents (目录)

1.  [Basic Data Class (基本数据类)](01_basic_data_class.md)
2.  [Default Values and `field` (默认值与`field`)](02_default_values_and_field.md)
3.  [Immutable Data Classes (不可变数据类)](03_immutable_data_classes.md)
4.  [Inheritance (继承)](04_inheritance.md)
