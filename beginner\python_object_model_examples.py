#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Python对象模型示例代码
Python Object Model Examples

演示Python对象模型的核心概念，包括对象身份、类型、值、可变性等。
Demonstrates core concepts of Python object model including object identity, type, value, mutability, etc.
"""

import sys
import gc
import weakref


def demonstrate_object_identity():
    """演示对象身份的概念"""
    print("=== 对象身份演示 ===")
    
    # 整数对象身份
    a = 100
    b = 100
    print(f"小整数 a=100, b=100:")
    print(f"  a is b: {a is b}")  # True，小整数缓存
    print(f"  id(a): {id(a)}, id(b): {id(b)}")
    
    # 大整数对象身份
    x = 1000
    y = 1000
    print(f"\n大整数 x=1000, y=1000:")
    print(f"  x is y: {x is y}")  # 可能是False
    print(f"  id(x): {id(x)}, id(y): {id(y)}")
    
    # 列表对象身份
    list1 = [1, 2, 3]
    list2 = [1, 2, 3]
    list3 = list1
    print(f"\n列表对象:")
    print(f"  list1 is list2: {list1 is list2}")  # False
    print(f"  list1 is list3: {list1 is list3}")  # True
    print(f"  list1 == list2: {list1 == list2}")  # True


def demonstrate_mutability():
    """演示可变性和不可变性"""
    print("\n=== 可变性演示 ===")
    
    # 不可变对象
    s = "hello"
    original_id = id(s)
    s += " world"
    print(f"字符串不可变:")
    print(f"  原始ID: {original_id}")
    print(f"  新ID: {id(s)}")
    print(f"  ID相同: {original_id == id(s)}")  # False
    
    # 可变对象
    lst = [1, 2, 3]
    original_id = id(lst)
    lst.append(4)
    print(f"\n列表可变:")
    print(f"  原始ID: {original_id}")
    print(f"  当前ID: {id(lst)}")
    print(f"  ID相同: {original_id == id(lst)}")  # True


def demonstrate_variable_binding():
    """演示变量绑定机制"""
    print("\n=== 变量绑定演示 ===")
    
    # 基本绑定
    a = [1, 2, 3]
    b = a  # b绑定到a引用的对象
    print(f"绑定前: a={a}, b={b}")
    print(f"a is b: {a is b}")
    
    # 修改对象
    a.append(4)
    print(f"修改a后: a={a}, b={b}")
    
    # 重新绑定
    a = [5, 6, 7]
    print(f"重新绑定a后: a={a}, b={b}")
    print(f"a is b: {a is b}")


def demonstrate_function_parameters():
    """演示函数参数传递"""
    print("\n=== 函数参数传递演示 ===")
    
    def modify_list(lst):
        """修改列表内容"""
        lst.append("modified")
        print(f"  函数内修改后: {lst}")
    
    def reassign_list(lst):
        """重新绑定参数"""
        lst = ["new", "list"]
        print(f"  函数内重新绑定: {lst}")
    
    original = ["original"]
    print(f"原始列表: {original}")
    
    modify_list(original.copy())  # 传递副本
    print(f"传递副本后: {original}")
    
    modify_list(original)  # 传递原对象
    print(f"传递原对象后: {original}")
    
    reassign_list(original)
    print(f"重新绑定参数后: {original}")


def demonstrate_memory_optimization():
    """演示内存优化机制"""
    print("\n=== 内存优化演示 ===")
    
    # 小整数缓存
    print("小整数缓存:")
    for i in [-1, 0, 1, 100, 256, 257]:
        a = i
        b = i
        print(f"  {i}: a is b = {a is b}")
    
    # 字符串驻留
    print("\n字符串驻留:")
    s1 = "hello"
    s2 = "hello"
    s3 = "hello world"
    s4 = "hello world"
    print(f"  简单字符串 'hello': s1 is s2 = {s1 is s2}")
    print(f"  复杂字符串 'hello world': s3 is s4 = {s3 is s4}")
    
    # 手动驻留
    s5 = sys.intern("hello world!")
    s6 = sys.intern("hello world!")
    print(f"  手动驻留: s5 is s6 = {s5 is s6}")


class LifecycleDemo:
    """用于演示对象生命周期的类"""
    
    def __init__(self, name):
        self.name = name
        print(f"  创建对象: {self.name}")
    
    def __del__(self):
        print(f"  销毁对象: {self.name}")


def demonstrate_object_lifecycle():
    """演示对象生命周期"""
    print("\n=== 对象生命周期演示 ===")
    
    # 创建对象
    obj = LifecycleDemo("测试对象")
    
    # 创建弱引用
    weak_ref = weakref.ref(obj)
    print(f"创建弱引用，对象存在: {weak_ref() is not None}")
    
    # 删除强引用
    del obj
    print(f"删除强引用后，对象存在: {weak_ref() is not None}")
    
    # 强制垃圾回收
    collected = gc.collect()
    print(f"垃圾回收，回收了{collected}个对象")
    print(f"垃圾回收后，对象存在: {weak_ref() is not None}")


def demonstrate_circular_reference():
    """演示循环引用问题"""
    print("\n=== 循环引用演示 ===")
    
    class Node:
        def __init__(self, value):
            self.value = value
            self.parent = None
            self.children = []
        
        def add_child(self, child):
            child.parent = self
            self.children.append(child)
        
        def __del__(self):
            print(f"  销毁节点: {self.value}")
    
    def create_cycle():
        root = Node("root")
        child = Node("child")
        root.add_child(child)
        
        # 创建弱引用观察
        return weakref.ref(root), weakref.ref(child)
    
    print("创建循环引用...")
    root_ref, child_ref = create_cycle()
    
    print(f"创建后对象存在: root={root_ref() is not None}, child={child_ref() is not None}")
    
    # 强制垃圾回收
    collected = gc.collect()
    print(f"垃圾回收后: root={root_ref() is not None}, child={child_ref() is not None}")


def demonstrate_reference_counting():
    """演示引用计数"""
    print("\n=== 引用计数演示 ===")
    
    obj = [1, 2, 3]
    print(f"创建对象，引用计数: {sys.getrefcount(obj)}")
    
    ref1 = obj
    print(f"添加引用1，引用计数: {sys.getrefcount(obj)}")
    
    ref2 = obj
    print(f"添加引用2，引用计数: {sys.getrefcount(obj)}")
    
    del ref1
    print(f"删除引用1，引用计数: {sys.getrefcount(obj)}")
    
    del ref2
    print(f"删除引用2，引用计数: {sys.getrefcount(obj)}")


def demonstrate_object_size():
    """演示对象大小"""
    print("\n=== 对象大小演示 ===")
    
    objects = [
        42,
        "hello",
        [1, 2, 3],
        {"a": 1, "b": 2},
        {1, 2, 3},
        (1, 2, 3),
    ]
    
    for obj in objects:
        print(f"  {type(obj).__name__}: {obj} -> {sys.getsizeof(obj)} bytes")


class CustomClass:
    """自定义类用于演示"""
    
    def __init__(self, value):
        self.value = value
    
    def __repr__(self):
        return f"CustomClass({self.value})"
    
    def __eq__(self, other):
        if isinstance(other, CustomClass):
            return self.value == other.value
        return False


def demonstrate_custom_objects():
    """演示自定义对象"""
    print("\n=== 自定义对象演示 ===")
    
    obj1 = CustomClass(42)
    obj2 = CustomClass(42)
    obj3 = obj1
    
    print(f"obj1: {obj1}")
    print(f"obj2: {obj2}")
    print(f"obj3: {obj3}")
    
    print(f"obj1 == obj2: {obj1 == obj2}")  # True (值相等)
    print(f"obj1 is obj2: {obj1 is obj2}")  # False (不同对象)
    print(f"obj1 is obj3: {obj1 is obj3}")  # True (同一对象)


def main():
    """主函数，运行所有演示"""
    print("Python对象模型深入演示")
    print("=" * 50)
    
    demonstrate_object_identity()
    demonstrate_mutability()
    demonstrate_variable_binding()
    demonstrate_function_parameters()
    demonstrate_memory_optimization()
    demonstrate_object_lifecycle()
    demonstrate_circular_reference()
    demonstrate_reference_counting()
    demonstrate_object_size()
    demonstrate_custom_objects()
    
    print("\n" + "=" * 50)
    print("演示完成！")


if __name__ == "__main__":
    main()
