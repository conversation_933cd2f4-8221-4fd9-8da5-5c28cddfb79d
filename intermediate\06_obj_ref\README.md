# Chapter 6: Object References, Mutability, and Recycling (对象引用、可变性与垃圾回收)

This chapter delves into the concepts of object references, mutability, and how Python manages memory through garbage collection. Understanding these concepts is key to avoiding common pitfalls and writing more efficient Python code.

本章深入探讨了对象引用、可变性以及Python如何通过垃圾回收来管理内存的概念。理解这些概念是避免常见陷阱和编写更高效Python代码的关键。

## Table of Contents (目录)

1.  [Variables are not Boxes (变量不是盒子)](01_variables_are_not_boxes.md)
2.  [Identity, Equality, and Aliases (标识、相等性和别名)](02_identity_equality_aliases.md)
3.  [Mutable vs. Immutable Types (可变类型与不可变类型)](03_mutable_vs_immutable.md)
4.  [Function Arguments and Mutability (函数参数与可变性)](04_function_arguments_and_mutability.md)
5.  [Garbage Collection (垃圾回收)](05_garbage_collection.md)
