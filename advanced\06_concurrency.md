# 6. Concurrency (并发)

Concurrency is the ability to handle multiple tasks at the same time. In Python, there are several ways to achieve concurrency, including multithreading, multiprocessing, and asynchronous I/O.

并发是指同时处理多个任务的能力。在Python中，有多种实现并发的方式，包括多线程、多进程和异步IO。

## Multithreading (多线程)

Multithreading allows you to create multiple threads in the same process, each of which can perform a different task. Due to Python's Global Interpreter Lock (GIL), multithreading cannot achieve true parallelism in CPU-bound tasks, but it can improve efficiency in I/O-bound tasks.

多线程允许你在同一个进程中创建多个线程，每个线程可以执行不同的任务。由于Python的全局解释器锁 (GIL)，多线程在CPU密集型任务中并不能实现真正的并行，但在I/O密集型任务中可以提高效率。

```python
import threading
import time

def worker(name):
    print(f"Thread {name}: starting")
    time.sleep(2)
    print(f"Thread {name}: finishing")

threads = []
for i in range(5):
    t = threading.Thread(target=worker, args=(i,))
    threads.append(t)
    t.start()

for t in threads:
    t.join()
```

## Multiprocessing (多进程)

Multiprocessing allows you to create multiple processes, each with its own Python interpreter and memory space. This allows you to achieve true parallelism on multi-core CPUs, thereby improving the performance of CPU-bound tasks.

多进程允许你创建多个进程，每个进程都有自己的Python解释器和内存空间。这可以让你在多核CPU上实现真正的并行，从而提高CPU密集型任务的性能。

```python
import multiprocessing
import time

def worker(name):
    print(f"Process {name}: starting")
    time.sleep(2)
    print(f"Process {name}: finishing")

if __name__ == "__main__":
    processes = []
    for i in range(5):
        p = multiprocessing.Process(target=worker, args=(i,))
        processes.append(p)
        p.start()

    for p in processes:
        p.join()
```

## Asynchronous I/O (异步IO)

Asynchronous I/O is a concurrent programming model that allows you to perform other tasks while waiting for I/O operations (such as network requests, file I/O) to complete. This allows you to achieve very high concurrency in I/O-bound tasks.

异步IO是一种并发编程模型，它允许你在等待I/O操作（如网络请求、文件读写）完成时，执行其他任务。这可以让你在I/O密集型任务中实现非常高的并发性。

```python
import asyncio

async def main():
    print('hello')
    await asyncio.sleep(1)
    print('world')

asyncio.run(main())
```