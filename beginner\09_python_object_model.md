# 9. Python对象模型深入 | Deep Dive into Python Object Model

理解Python的对象模型是成为Python专家的关键。本章将深入探讨Python中一切皆对象的概念，以及对象的身份、类型和值。

Understanding Python's object model is key to becoming a Python expert. This chapter will deeply explore the concept that everything is an object in Python, as well as object identity, type, and value.

## 9.1 一切皆对象 | Everything is an Object

在Python中，一切都是对象，包括数字、字符串、函数、类，甚至模块。

In Python, everything is an object, including numbers, strings, functions, classes, and even modules.

```python
# 数字是对象
x = 42
print(f"数字的类型: {type(x)}")
print(f"数字的ID: {id(x)}")
print(f"数字的方法: {dir(x)}")

# 字符串是对象
s = "hello"
print(f"字符串的类型: {type(s)}")
print(f"字符串的ID: {id(s)}")

# 函数是对象
def my_func():
    pass

print(f"函数的类型: {type(my_func)}")
print(f"函数的ID: {id(my_func)}")
print(f"函数的属性: {my_func.__name__}")

# 类是对象
class MyClass:
    pass

print(f"类的类型: {type(MyClass)}")
print(f"类的ID: {id(MyClass)}")
```

## 9.2 对象的三要素 | Three Pillars of Objects

每个Python对象都有三个基本属性：身份(identity)、类型(type)和值(value)。

Every Python object has three fundamental attributes: identity, type, and value.

### 身份 (Identity)

对象的身份是其在内存中的唯一标识符，可以通过`id()`函数获取。

```python
# 对象身份示例
a = [1, 2, 3]
b = [1, 2, 3]
c = a

print(f"a的ID: {id(a)}")
print(f"b的ID: {id(b)}")  # 不同的对象，不同的ID
print(f"c的ID: {id(c)}")  # 同一个对象，相同的ID

# is 操作符比较身份
print(f"a is b: {a is b}")  # False
print(f"a is c: {a is c}")  # True

# == 操作符比较值
print(f"a == b: {a == b}")  # True
print(f"a == c: {a == c}")  # True
```

### 类型 (Type)

对象的类型决定了对象支持的操作和可能的值。

```python
# 类型示例
x = 42
y = "42"
z = [4, 2]

print(f"x的类型: {type(x)}")  # <class 'int'>
print(f"y的类型: {type(y)}")  # <class 'str'>
print(f"z的类型: {type(z)}")  # <class 'list'>

# 类型检查
print(f"isinstance(x, int): {isinstance(x, int)}")
print(f"isinstance(y, str): {isinstance(y, str)}")
print(f"isinstance(z, list): {isinstance(z, list)}")
```

### 值 (Value)

对象的值是其包含的数据。

```python
# 值的示例
a = 10
b = 10

# 小整数缓存机制
print(f"a is b: {a is b}")  # True (小整数被缓存)

# 大整数不缓存
x = 1000
y = 1000
print(f"x is y: {x is y}")  # 可能是False

# 强制创建新对象
x = 1000
y = int("1000")
print(f"x is y: {x is y}")  # False
print(f"x == y: {x == y}")  # True
```

## 9.3 可变性与不可变性 | Mutability and Immutability

Python对象分为可变(mutable)和不可变(immutable)两类。

Python objects are divided into mutable and immutable categories.

### 不可变对象 (Immutable Objects)

```python
# 不可变对象：int, float, str, tuple, frozenset
x = 10
original_id = id(x)
x += 1  # 创建新对象
print(f"原始ID: {original_id}")
print(f"新ID: {id(x)}")
print(f"ID是否相同: {original_id == id(x)}")  # False

# 字符串不可变
s = "hello"
original_id = id(s)
s += " world"  # 创建新字符串对象
print(f"字符串原始ID: {original_id}")
print(f"字符串新ID: {id(s)}")

# 元组不可变
t = (1, 2, 3)
original_id = id(t)
# t[0] = 10  # 这会引发TypeError
try:
    t[0] = 10
except TypeError as e:
    print(f"元组不可变错误: {e}")
```

### 可变对象 (Mutable Objects)

```python
# 可变对象：list, dict, set, 自定义类实例
lst = [1, 2, 3]
original_id = id(lst)
lst.append(4)  # 修改原对象
print(f"列表原始ID: {original_id}")
print(f"列表当前ID: {id(lst)}")
print(f"ID是否相同: {original_id == id(lst)}")  # True

# 字典可变
d = {"a": 1, "b": 2}
original_id = id(d)
d["c"] = 3  # 修改原对象
print(f"字典ID是否相同: {original_id == id(d)}")  # True

# 集合可变
s = {1, 2, 3}
original_id = id(s)
s.add(4)  # 修改原对象
print(f"集合ID是否相同: {original_id == id(s)}")  # True
```

## 9.4 变量绑定机制 | Variable Binding Mechanism

Python中的变量实际上是对象的引用，而不是存储值的容器。

Variables in Python are actually references to objects, not containers that store values.

```python
# 变量绑定示例
a = [1, 2, 3]
b = a  # b绑定到a引用的同一个对象

print(f"a: {a}")
print(f"b: {b}")
print(f"a is b: {a is b}")  # True

# 修改对象会影响所有引用
a.append(4)
print(f"修改后 a: {a}")
print(f"修改后 b: {b}")  # b也发生了变化

# 重新绑定变量
a = [5, 6, 7]  # a绑定到新对象
print(f"重新绑定后 a: {a}")
print(f"重新绑定后 b: {b}")  # b仍然引用原对象
print(f"a is b: {a is b}")  # False
```

### 函数参数传递

```python
def modify_list(lst):
    """修改列表对象"""
    lst.append(4)
    print(f"函数内 lst: {lst}")

def reassign_list(lst):
    """重新绑定参数"""
    lst = [7, 8, 9]  # 局部重新绑定
    print(f"函数内重新绑定 lst: {lst}")

# 测试函数参数传递
original_list = [1, 2, 3]
print(f"原始列表: {original_list}")

modify_list(original_list)
print(f"修改后列表: {original_list}")  # 列表被修改

reassign_list(original_list)
print(f"重新绑定后列表: {original_list}")  # 列表未变化
```

## 9.5 对象的生命周期 | Object Lifecycle

```python
import gc
import weakref

class MyClass:
    def __init__(self, name):
        self.name = name
        print(f"创建对象: {self.name}")
    
    def __del__(self):
        print(f"销毁对象: {self.name}")

# 对象创建
obj1 = MyClass("对象1")
obj2 = MyClass("对象2")

# 创建弱引用
weak_ref = weakref.ref(obj1)
print(f"弱引用是否存活: {weak_ref() is not None}")

# 删除强引用
del obj1
print(f"删除强引用后，弱引用是否存活: {weak_ref() is not None}")

# 强制垃圾回收
gc.collect()
print(f"垃圾回收后，弱引用是否存活: {weak_ref() is not None}")
```

## 9.6 内存优化机制 | Memory Optimization Mechanisms

### 小整数缓存

```python
# 小整数缓存 (-5 到 256)
a = 100
b = 100
print(f"小整数 a is b: {a is b}")  # True

# 大整数不缓存
x = 1000
y = 1000
print(f"大整数 x is y: {x is y}")  # 可能是False

# 查看小整数缓存范围
import sys
print(f"小整数缓存范围: {sys.int_info}")
```

### 字符串驻留

```python
# 字符串驻留机制
s1 = "hello"
s2 = "hello"
print(f"字符串驻留 s1 is s2: {s1 is s2}")  # True

# 动态创建的字符串可能不驻留
s3 = "hel" + "lo"
print(f"动态字符串 s1 is s3: {s1 is s3}")  # 可能是True

# 包含特殊字符的字符串通常不驻留
s4 = "hello world!"
s5 = "hello world!"
print(f"特殊字符串 s4 is s5: {s4 is s5}")  # 可能是False

# 手动驻留
import sys
s6 = sys.intern("hello world!")
s7 = sys.intern("hello world!")
print(f"手动驻留 s6 is s7: {s6 is s7}")  # True
```

## 9.7 实践练习 | Practical Exercises

```python
# 练习1: 理解对象身份
def exercise_object_identity():
    """练习对象身份的概念"""
    # TODO: 创建两个相同值的列表，比较它们的身份和值
    # TODO: 创建一个列表的别名，验证身份关系
    pass

# 练习2: 可变性陷阱
def exercise_mutability_trap():
    """练习可变性相关的常见陷阱"""
    # TODO: 演示默认参数陷阱
    # TODO: 演示浅拷贝vs深拷贝
    pass

# 练习3: 内存优化
def exercise_memory_optimization():
    """练习内存优化机制"""
    # TODO: 测试不同大小整数的身份
    # TODO: 测试字符串驻留机制
    pass

if __name__ == "__main__":
    exercise_object_identity()
    exercise_mutability_trap()
    exercise_memory_optimization()
```

## 总结 | Summary

理解Python对象模型是编写高效、正确Python代码的基础：

1. **一切皆对象**: Python中的所有数据都是对象
2. **三要素**: 每个对象都有身份、类型和值
3. **可变性**: 区分可变和不可变对象的重要性
4. **变量绑定**: 变量是对象的引用，不是容器
5. **内存优化**: Python的各种内存优化机制

Understanding Python's object model is fundamental to writing efficient and correct Python code:

1. **Everything is an object**: All data in Python are objects
2. **Three pillars**: Every object has identity, type, and value
3. **Mutability**: The importance of distinguishing mutable and immutable objects
4. **Variable binding**: Variables are references to objects, not containers
5. **Memory optimization**: Python's various memory optimization mechanisms
