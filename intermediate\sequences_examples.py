# sequences_examples.py

# Slicing examples (切片示例)
my_list = list(range(10))
print(f"Original list: {my_list}")
print(f"First 3 items: {my_list[:3]}")
print(f"Items from index 3 to 6: {my_list[3:7]}")
print(f"Last 3 items: {my_list[-3:]}")
print(f"Every other item: {my_list[::2]}")
print(f"Reversed list: {my_list[::-1]}")

# Hashing examples (哈希示例)
print("\n--- Hashing ---")
print(f"hash(1) = {hash(1)}")
print(f"hash('hello') = {hash('hello')}")
print(f"hash((1, 2)) = {hash((1, 2))}")

try:
    print(hash([1, 2]))
except TypeError as e:
    print(f"hash([1, 2]) raises: {e}")
