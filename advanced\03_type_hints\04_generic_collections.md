# 3.4 Generic Collections (泛型集合)

Starting with Python 3.9, you can use the standard collection types as generic types. This means that you can use `list`, `dict`, and `set` in type hints to specify the types of their elements.

从Python 3.9开始，你可以使用标准集合类型作为泛型类型。这意味着你可以在类型提示中使用 `list`、`dict` 和 `set` 来指定其元素的类型。

## Generic `list`

```python
# code/generic_list.py

def print_lengths(items: list[str]) -> None:
    for item in items:
        print(len(item))

print_lengths(["apple", "banana", "cherry"])
```

## Generic `dict`

```python
# code/generic_dict.py

def print_person(person: dict[str, int]) -> None:
    for key, value in person.items():
        print(f"{key}: {value}")

print_person({"age": 30, "height": 175})
```

## Generic `set`

```python
# code/generic_set.py

def print_set(my_set: set[int]) -> None:
    for item in my_set:
        print(item)

print_set({1, 2, 3})
```

This feature makes type hints more concise and readable, as you no longer need to import `List`, `Dict`, and `Set` from the `typing` module.

这个特性使得类型提示更加简洁和易读，因为你不再需要从 `typing` 模块中导入 `List`、`Dict` 和 `Set`。
