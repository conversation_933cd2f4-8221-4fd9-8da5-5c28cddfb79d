# Chapter 4: Text vs. Bytes (文本与字节)

This chapter explores the fundamental difference between strings of human text and sequences of raw bytes. We will cover character encodings, the `struct` module for handling binary data, and best practices for handling text and binary files.

本章探讨了人类文本字符串和原始字节序列之间的根本区别。我们将涵盖字符编码、用于处理二进制数据的 `struct` 模块，以及处理文本和二进制文件的最佳实践。

## Table of Contents (目录)

1.  [Character Encodings (字符编码)](01_character_encodings.md)
2.  [Handling Text Files (处理文本文件)](02_handling_text_files.md)
3.  [Handling Binary Files with `struct` (使用`struct`处理二进制文件)](03_handling_binary_files_with_struct.md)
