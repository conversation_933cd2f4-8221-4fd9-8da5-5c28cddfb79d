# 1. Introduction to Python (Python 简介)

Welcome to the world of Python! Python is a high-level, interpreted programming language known for its readability and simplicity. It's a great language for beginners and is widely used in various fields like web development, data science, artificial intelligence, and more.

欢迎来到Python的世界！Python是一种高级、解释型的编程语言，以其可读性和简洁性而闻名。对于初学者来说，它是一门极好的语言，并广泛应用于Web开发、数据科学、人工智能等领域。

## Why Python? (为什么选择Python？)

*   **Easy to Learn (易于学习):** Python has a simple syntax that is easy to read and write, making it an excellent choice for beginners. (Python的语法简单，易于读写，是初学者的绝佳选择。)
*   **Versatile (功能多样):** Python can be used for a wide range of applications, from simple scripts to complex web applications. (Python可用于各种应用，从简单的脚本到复杂的Web应用。)
*   **Large Community (庞大的社区):** Python has a large and active community, which means you can find plenty of resources and support online. (Python拥有一个庞大而活跃的社区，这意味着你可以在网上找到大量的资源和支持。)

## Getting Started (入门)

To get started with Python, you'll need to have it installed on your computer. You can download the latest version of Python from the official website: [https://www.python.org/downloads/](https://www.python.org/downloads/)

要开始使用Python，你需要在你的电脑上安装它。你可以从官方网站下载最新版本的Python：[https://www.python.org/downloads/](https://www.python.org/downloads/)

Once you have Python installed, you can open a terminal or command prompt and type `python` to start the Python interpreter. You should see something like this:

安装Python后，你可以打开终端或命令提示符，输入 `python` 来启动Python解释器。你应该会看到类似这样的信息：

```
Python 3.9.7 (default, Sep 16 2021, 16:59:28) [MSC v.1916 64 bit (AMD64)] on win32
Type "help", "copyright", "credits" or "license" for more information.
>>>
```

Now you're ready to start writing Python code! (现在你已经准备好开始编写Python代码了！)

## Your First Python Program (你的第一个Python程序)

Let's write a simple program that prints "Hello, World!" to the console. In the Python interpreter, type the following and press Enter:

让我们来编写一个简单的程序，在控制台打印 "Hello, World!"。在Python解释器中，输入以下内容并按回车键：

```python
print("Hello, World!")
```

You should see the following output: (你应该会看到以下输出：)

```
Hello, World!
```

Congratulations! You've just written your first Python program. (恭喜！你刚刚编写了你的第一个Python程序。)

Let's try something a little more interactive. This program will ask for your name and then say hello.

我们来尝试一些更具交互性的东西。这个程序会询问你的名字，然后向你问好。

```python
# 1. Use the input() function to get the user's name
#    (使用 input() 函数获取用户的名字)
name = input("What is your name? ")

# 2. Use the print() function to say hello
#    (使用 print() 函数来问好)
print("Hello, " + name + "!")
```

When you run this code, it will first ask for your name. After you type your name and press Enter, it will greet you.

当你运行这段代码时，它会首先询问你的名字。在你输入名字并按回车后，它会向你问好。

```
What is your name? Alice
Hello, Alice!
```