# 3.1 Common Dictionary Methods (常用字典方法)

The `dict` type has a rich set of methods for various operations. Let's explore some of the most common ones.

`dict` 类型有一套丰富的方法，可用于各种操作。让我们来探讨一些最常用的方法。

## `update`

The `update` method can be used to merge a dictionary with another dictionary or with an iterable of key/value pairs.

`update` 方法可用于将一个字典与另一个字典或键/值对的可迭代对象合并。

```python
# code/dict_update.py
d1 = {'a': 1, 'b': 2}
d2 = {'b': 3, 'c': 4}
d1.update(d2)
print(f"d1 after update: {d1}")
```

## `get` and `setdefault`

These methods are useful for handling missing keys.

这些方法对于处理缺失的键非常有用。

*   `get(key, default)`: Returns the value for `key` if `key` is in the dictionary, else `default`. If `default` is not given, it defaults to `None`.
*   `setdefault(key, default)`: If `key` is in the dictionary, return its value. If not, insert `key` with a value of `default` and return `default`.

```python
# code/dict_get_setdefault.py
my_dict = {'a': 1}
print(f"my_dict.get('b', 'N/A'): {my_dict.get('b', 'N/A')}")
print(f"my_dict after get: {my_dict}")

print(f"my_dict.setdefault('b', 'N/A'): {my_dict.setdefault('b', 'N/A')}")
print(f"my_dict after setdefault: {my_dict}")
```

## Dictionary Views (字典视图)

The methods `keys()`, `values()`, and `items()` return dictionary views. A dictionary view is a dynamic view on the dictionary’s entries, which means that when the dictionary changes, the view reflects these changes.

`keys()`、`values()` 和 `items()` 方法返回字典视图。字典视图是字典条目的动态视图，这意味着当字典更改时，视图会反映这些更改。

```python
# code/dict_views.py
d = dict(a=10, b=20, c=30)
values = d.values()
print(f"Original values: {values}")
d['d'] = 40
print(f"Values after adding a key: {values}")
```
