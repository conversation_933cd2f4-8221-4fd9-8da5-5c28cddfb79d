# type_hints_examples.py

from typing import List, Optional

# Basic type hints (基本类型提示)
def greeting(name: str) -> str:
    return 'Hello ' + name

print(greeting("Alice"))

# Type aliases (类型别名)
Vector = List[float]

def scale(scalar: float, vector: Vector) -> Vector:
    return [scalar * num for num in vector]

print(scale(2.0, [1.0, -4.2, 5.4]))

# Optional type (Optional类型)
def greet_optional(name: Optional[str] = None) -> str:
    if name is None:
        name = "stranger"
    return f"Hello, {name}"

print(greet_optional())
print(greet_optional("Bob"))
