# data_structures_examples.py

# This file demonstrates the use of lists, tuples, and dictionaries in Python.
# 这个文件演示了Python中列表、元组和字典的用法。

# Lists (列表)
print("--- Lists ---")
fruits = ["apple", "banana", "cherry"]
print(f"Original list: {fruits}")
fruits.append("orange")
print(f"After appending 'orange': {fruits}")
fruits.remove("banana")
print(f"After removing 'banana': {fruits}")
print(f"The first fruit is: {fruits[0]}")

# Tuples (元组)
print("\n--- Tuples ---")
coordinates = (10, 20)
print(f"Coordinates: {coordinates}")
print(f"x-coordinate: {coordinates[0]}")

# Dictionaries (字典)
print("\n--- Dictionaries ---")
person = {"name": "John", "age": 30}
print(f"Original dictionary: {person}")
person["city"] = "New York"
print(f"After adding 'city': {person}")
del person["age"]
print(f"After deleting 'age': {person}")
print(f"Name: {person['name']}")
