# 6.1 Variables are not Boxes (变量不是盒子)

In some programming languages, variables are like boxes where you store values. In Python, variables are more like labels that you attach to objects.

在某些编程语言中，变量就像存放值的盒子。在Python中，变量更像是你附加到对象上的标签。

## Assignment (赋值)

When you write `a = [1, 2, 3]`, you are not putting the list `[1, 2, 3]` inside a box called `a`. Instead, you are creating a list object and attaching the label `a` to it.

当你写 `a = [1, 2, 3]` 时，你并不是把列表 `[1, 2, 3]` 放进一个叫做 `a` 的盒子里。相反，你正在创建一个列表对象，并将标签 `a` 附加到它上面。

When you then write `b = a`, you are not copying the list. You are creating a new label `b` and attaching it to the *same* list object that `a` is attached to.

然后，当你写 `b = a` 时，你不是在复制列表。你正在创建一个新的标签 `b`，并将其附加到 `a` 所附加的*同一个*列表对象上。

```python
# code/variables_as_labels.py

a = [1, 2, 3]
b = a

print(f"a: {a}")
print(f"b: {b}")

# Modifying the list through b also affects a
# (通过b修改列表也会影响a)
b.append(4)

print(f"After b.append(4):")
print(f"a: {a}")
print(f"b: {b}")
```

This is a fundamental concept in Python that is important to understand, especially when working with mutable objects.

这是Python中一个需要理解的基本概念，尤其是在使用可变对象时。
