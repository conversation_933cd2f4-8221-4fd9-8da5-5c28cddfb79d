# code/lru_cache_example.py

import functools
import time

@functools.lru_cache()
def fibonacci(n):
    if n < 2:
        return n
    return fibonacci(n - 2) + fibonacci(n - 1)

start_time = time.time()
result = fibonacci(30)
end_time = time.time()
print(f"fibonacci(30) = {result}")
print(f"Duration: {end_time - start_time:.4f}s")

# Without lru_cache, this would be much slower
# (如果没有lru_cache，这会慢得多)
def fibonacci_no_cache(n):
    if n < 2:
        return n
    return fibonacci_no_cache(n - 2) + fibonacci_no_cache(n - 1)

start_time = time.time()
result = fibonacci_no_cache(30)
end_time = time.time()
print(f"\nfibonacci_no_cache(30) = {result}")
print(f"Duration without cache: {end_time - start_time:.4f}s")
