# Chapter 2: Sequences, Hashing, and Slicing (序列、哈希和切片)

This chapter provides a deep dive into Python's sequence data structures. We will cover list comprehensions, generator expressions, tuples as records, slicing, and the `bisect` module for managing sorted sequences.

本章深入探讨了Python的序列数据结构。我们将涵盖列表推导、生成器表达式、作为记录的元组、切片以及用于管理已排序序列的 `bisect` 模块。

## Table of Contents (目录)

1.  [List Comprehensions and Generator Expressions (列表推导与生成器表达式)](01_list_comprehensions_and_generator_expressions.md)
2.  [Tuples as Records (元组作为记录)](02_tuples_as_records.md)
3.  [Slicing (切片)](03_slicing.md)
4.  [Managing Sorted Sequences with `bisect` (使用`bisect`管理已排序序列)](04_managing_sorted_sequences_with_bisect.md)
