# frenchdeck.py

import collections

Card = collections.namedtuple('Card', ['rank', 'suit'])

class FrenchDeck:
    ranks = [str(n) for n in range(2, 11)] + list('JQKA')
    suits = 'spades diamonds clubs hearts'.split()

    def __init__(self):
        self._cards = [Card(rank, suit) for suit in self.suits
                                        for rank in self.ranks]

    def __len__(self):
        return len(self._cards)

    def __getitem__(self, position):
        return self._cards[position]


if __name__ == '__main__':
    deck = FrenchDeck()
    print(f"Length of deck: {len(deck)}")
    print(f"First card: {deck[0]}")
    print(f"Last card: {deck[-1]}")

    from random import choice
    print(f"Random card: {choice(deck)}")

    print("\n--- Slicing ---")
    print(f"First 3 cards: {deck[:3]}")
    print(f"Aces: {deck[12::13]}")

    print("\n--- Iteration ---")
    for card in deck[:5]:
        print(card)

