# 3.3 Optional and Union Types (`Optional`与`Union`类型)

## `Optional`

The `Optional` type is used to indicate that a parameter can be of a certain type or `None`.

`Optional` 类型用于指示参数可以是某个类型或 `None`。

```python
# code/optional_type.py

from typing import Optional

def greet(name: Optional[str] = None) -> str:
    if name is None:
        name = "stranger"
    return f"Hello, {name}"

print(greet())
print(greet("Alice"))
```

`Optional[str]` is a shortcut for `Union[str, None]`.

`Optional[str]` 是 `Union[str, None]` 的简写。

## `Union`

The `Union` type is used to indicate that a parameter can be one of several types.

`Union` 类型用于指示参数可以是几种类型之一。

```python
# code/union_type.py

from typing import Union

def process(item: Union[int, str]) -> None:
    if isinstance(item, int):
        print(f"Processing an integer: {item}")
    else:
        print(f"Processing a string: {item}")

process(1)
process("hello")
```
