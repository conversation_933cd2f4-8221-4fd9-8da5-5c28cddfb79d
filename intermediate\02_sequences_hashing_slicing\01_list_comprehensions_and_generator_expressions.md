# 2.1 List Comprehensions and Generator Expressions (列表推导与生成器表达式)

List comprehensions (listcomps) and generator expressions (genexps) are powerful features for creating sequences in a concise and readable way.

列表推导和生成器表达式是在Python中以简洁易读的方式创建序列的强大功能。

## List Comprehensions (列表推导)

A list comprehension provides a concise way to create lists. It consists of brackets containing an expression followed by a `for` clause, then zero or more `for` or `if` clauses.

列表推导提供了一种创建列表的简洁方法。它由一个方括号组成，其中包含一个表达式，后跟一个 `for` 子句，然后是零个或多个 `for` 或 `if` 子句。

```python
# code/listcomp_examples.py

# A simple list comprehension (一个简单的列表推导)
squares = [x**2 for x in range(10)]
print(f"Squares: {squares}")

# A list comprehension with a condition (带条件的列表推导)
even_squares = [x**2 for x in range(10) if x % 2 == 0]
print(f"Even squares: {even_squares}")
```

## Generator Expressions (生成器表达式)

Generator expressions use the same syntax as list comprehensions, but are enclosed in parentheses rather than brackets. They create an iterator that yields values one by one, which is more memory-efficient than creating a full list at once.

生成器表达式使用与列表推导相同的语法，但用圆括号而不是方括号括起来。它们创建一个逐个产生值的迭代器，这比一次性创建一个完整的列表更节省内存。

```python
# code/genexp_examples.py

# A generator expression (一个生成器表达式)
squares_gen = (x**2 for x in range(10))
print(f"Squares generator: {squares_gen}")

# To get the values, you can iterate over the generator
# (要获取值，你可以遍历生成器)
for square in squares_gen:
    print(square, end=" ")
print()
```
