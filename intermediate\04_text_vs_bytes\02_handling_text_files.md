# 4.2 Handling Text Files (处理文本文件)

When working with text files in Python, it's important to specify the encoding. If you don't, Python will use the system's default encoding, which can lead to problems if the file was saved with a different encoding.

在Python中处理文本文件时，指定编码非常重要。如果你不这样做，Python将使用系统的默认编码，如果文件是用不同的编码保存的，这可能会导致问题。

## Reading and Writing Text Files (读写文本文件)

The `open()` function has an `encoding` parameter that you should always use when working with text files.

`open()` 函数有一个 `encoding` 参数，在处理文本文件时应始终使用该参数。

```python
# code/text_file_handling.py

# Writing to a text file with a specific encoding
# (使用特定编码写入文本文件)
with open('hello.txt', 'w', encoding='utf-8') as f:
    f.write('你好, world!')

# Reading from a text file with the correct encoding
# (使用正确的编码从文本文件中读取)
with open('hello.txt', 'r', encoding='utf-8') as f:
    content = f.read()
    print(f"File content: {content}")
```

## The `locale` Module ( `locale` 模块)

The `locale` module can be used to get the user's preferred encoding.

`locale` 模块可用于获取用户的首选编码。

```python
# code/locale_example.py

import locale

print(f"Preferred encoding: {locale.getpreferredencoding()}")
```
