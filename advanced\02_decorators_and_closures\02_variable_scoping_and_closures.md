# 2.2 Variable Scoping and Closures (变量作用域和闭包)

A closure is a function that remembers and has access to its lexical scope, even when that scope is no longer active. This is possible because the inner function carries a reference to the environment of the outer function.

闭包是一个函数，它记住并可以访问其词法作用域，即使该作用域不再活动。这是可能的，因为内部函数带有对外部函数环境的引用。

## The `nonlocal` Keyword ( `nonlocal` 关键字)

In Python 3, you can use the `nonlocal` keyword to assign a new value to a variable in an outer (but not global) scope.

在Python 3中，你可以使用 `nonlocal` 关键字为外部（但非全局）作用域中的变量赋新值。

```python
# code/nonlocal_example.py

def make_averager():
    count = 0
    total = 0

    def averager(new_value):
        nonlocal count, total
        count += 1
        total += new_value
        return total / count

    return averager

avg = make_averager()
print(avg(10))
print(avg(11))
print(avg(12))
```

Without `nonlocal`, the `averager` function would create new local variables `count` and `total` instead of modifying the ones in the outer scope.

如果没有 `nonlocal`，`averager` 函数将创建新的局部变量 `count` 和 `total`，而不是修改外部作用域中的变量。
