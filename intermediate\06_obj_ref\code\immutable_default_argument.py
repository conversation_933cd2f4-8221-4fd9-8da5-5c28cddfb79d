# code/immutable_default_argument.py

class Bus:

    def __init__(self, passengers=None):
        if passengers is None:
            self.passengers = []
        else:
            self.passengers = list(passengers)

    def pick(self, name):
        self.passengers.append(name)

    def drop(self, name):
        self.passengers.remove(name)

bus1 = Bus(['<PERSON>', '<PERSON>'])
bus1.pick('Charlie')
print(f"bus1.passengers: {bus1.passengers}")

bus2 = Bus()
bus2.pick('Carrie')
print(f"bus2.passengers: {bus2.passengers}")

bus3 = Bus()
print(f"bus3.passengers: {bus3.passengers}")

bus3.pick('Dave')
print(f"bus2.passengers after bus3.pick('<PERSON>'): {bus2.passengers}")
