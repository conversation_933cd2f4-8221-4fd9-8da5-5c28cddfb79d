# Chapter 1: The Python Data Model (Python数据模型)

This chapter explores the Python data model, a key aspect of writing idiomatic Python code. By understanding and using the special methods (dunder methods), you can make your custom objects behave like built-in types, leveraging the richness of the Python language.

本章探讨了Python数据模型，这是编写地道Python代码的关键。通过理解和使用特殊方法（双下划线方法），你可以让你的自定义对象表现得像内置类型一样，从而利用Python语言的丰富性。

## Table of Contents (目录)

1.  [Introduction to the Data Model (数据模型简介)](01_introduction.md)
2.  [A Pythonic Card Deck (一个Pythonic的扑克牌)](02_pythonic_card_deck.md)
3.  [Emulating Numeric Types (模拟数字类型)](03_emulating_numeric_types.md)
4.  [String Representation: `__repr__` vs `__str__` (字符串表示)](04_repr_vs_str.md)
5.  [Boolean Value of a Custom Type (自定义类型的布尔值)](05_boolean_value.md)
6.  [Overview of Special Methods (特殊方法概述)](06_special_methods_overview.md)
