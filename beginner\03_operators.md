# 3. Operators (运算符)

Operators are special symbols in Python that carry out arithmetic or logical computation. The value that the operator operates on is called the operand.

运算符是Python中的特殊符号，用于执行算术或逻辑计算。运算符操作的值称为操作数。

## Arithmetic Operators (算术运算符)

| Operator | Description    | Example     | Result (结果) |
| :---:    | :---:          | :---:       | :---:         |
| +        | Addition (加法)       | `10 + 5`    | `15`          |
| -        | Subtraction (减法)    | `10 - 5`    | `5`           |
| *        | Multiplication (乘法) | `10 * 5`    | `50`          |
| /        | Division (除法)       | `10 / 5`    | `2.0`         |
| %        | Modulus (取模)        | `10 % 3`    | `1`           |
| **       | Exponentiation (幂) | `2 ** 3`    | `8`           |
| //       | Floor division (整除) | `10 // 3`   | `3`           |

```python
# Example usage (示例用法)
x = 10
y = 3

print("x + y =", x + y)
print("x - y =", x - y)
print("x * y =", x * y)
print("x / y =", x / y)
print("x % y =", x % y)
print("x ** y =", x ** y)
print("x // y =", x // y)
```

## Comparison Operators (比较运算符)

| Operator | Description                  | Example     | Result (结果) |
| :---:    | :---:                        | :---:       | :---:         |
| ==       | Equal to (等于)                     | `5 == 5`    | `True`        |
| !=       | Not equal to (不等于)                 | `5 != 3`    | `True`        |
| >        | Greater than (大于)                 | `5 > 3`     | `True`        |
| <        | Less than (小于)                    | `5 < 3`     | `False`       |
| >=       | Greater than or equal to (大于等于)     | `5 >= 5`    | `True`        |
| <=       | Less than or equal to (小于等于)        | `5 <= 3`    | `False`       |

```python
# Example usage (示例用法)
x = 5
y = 3

print("x == y is", x == y)
print("x != y is", x != y)
print("x > y is", x > y)
print("x < y is", x < y)
print("x >= y is", x >= y)
print("x <= y is", x <= y)
```

## Logical Operators (逻辑运算符)

| Operator | Description                                            | Example                 | Result (结果) |
| :---:    | :---:                                                  | :---:                   | :---:         |
| and      | Returns `True` if both statements are true (与)             | `True and False`        | `False`       |
| or       | Returns `True` if one of the statements is true (或)        | `True or False`         | `True`        |
| not      | Reverse the result, returns `False` if the result is true (非) | `not True`              | `False`       |

```python
# Example usage (示例用法)
x = True
y = False

print("x and y is", x and y)
print("x or y is", x or y)
print("not x is", not x)
```