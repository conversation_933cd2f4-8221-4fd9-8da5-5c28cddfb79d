#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
变量和数据类型练习题
Variables and Data Types Exercises

完成以下练习来巩固你对Python变量和数据类型的理解。
Complete the following exercises to reinforce your understanding of Python variables and data types.
"""

# 练习1: 基本变量操作
# Exercise 1: Basic Variable Operations
def exercise_1():
    """
    创建以下变量并打印它们的值和类型：
    - 你的姓名 (字符串)
    - 你的年龄 (整数)
    - 你的身高 (浮点数，单位：米)
    - 你是否是学生 (布尔值)
    """
    print("=== 练习1: 基本变量操作 ===")
    
    # 在这里写你的代码
    # TODO: 创建变量
    name = "张三"  # 示例，请修改为你的信息
    age = 20
    height = 1.75
    is_student = True
    
    # TODO: 打印变量值和类型
    print(f"姓名: {name}, 类型: {type(name)}")
    print(f"年龄: {age}, 类型: {type(age)}")
    print(f"身高: {height}, 类型: {type(height)}")
    print(f"是否学生: {is_student}, 类型: {type(is_student)}")


# 练习2: 类型转换
# Exercise 2: Type Conversion
def exercise_2():
    """
    完成以下类型转换任务：
    1. 将字符串 "123" 转换为整数
    2. 将整数 456 转换为浮点数
    3. 将浮点数 78.9 转换为整数
    4. 将布尔值 True 转换为字符串
    """
    print("\n=== 练习2: 类型转换 ===")
    
    # TODO: 完成类型转换
    str_num = "123"
    int_num = 456
    float_num = 78.9
    bool_val = True
    
    # 在这里进行转换
    converted_int = int(str_num)
    converted_float = float(int_num)
    converted_int_from_float = int(float_num)
    converted_str = str(bool_val)
    
    print(f"'{str_num}' -> {converted_int} (类型: {type(converted_int)})")
    print(f"{int_num} -> {converted_float} (类型: {type(converted_float)})")
    print(f"{float_num} -> {converted_int_from_float} (类型: {type(converted_int_from_float)})")
    print(f"{bool_val} -> '{converted_str}' (类型: {type(converted_str)})")


# 练习3: 字符串操作
# Exercise 3: String Operations
def exercise_3():
    """
    给定字符串 "  Python Programming  "，完成以下操作：
    1. 去除首尾空格
    2. 转换为大写
    3. 转换为小写
    4. 替换 "Python" 为 "Java"
    5. 检查是否包含 "gram"
    """
    print("\n=== 练习3: 字符串操作 ===")
    
    text = "  Python Programming  "
    print(f"原始字符串: '{text}'")
    
    # TODO: 完成字符串操作
    stripped = text.strip()
    upper_case = text.upper()
    lower_case = text.lower()
    replaced = text.replace("Python", "Java")
    contains_gram = "gram" in text
    
    print(f"去除空格: '{stripped}'")
    print(f"大写: '{upper_case}'")
    print(f"小写: '{lower_case}'")
    print(f"替换后: '{replaced}'")
    print(f"包含'gram': {contains_gram}")


# 练习4: 数字系统转换
# Exercise 4: Number System Conversion
def exercise_4():
    """
    将十进制数 42 转换为：
    1. 二进制
    2. 八进制
    3. 十六进制
    """
    print("\n=== 练习4: 数字系统转换 ===")
    
    decimal_num = 42
    
    # TODO: 进行进制转换
    binary = bin(decimal_num)
    octal = oct(decimal_num)
    hexadecimal = hex(decimal_num)
    
    print(f"十进制: {decimal_num}")
    print(f"二进制: {binary}")
    print(f"八进制: {octal}")
    print(f"十六进制: {hexadecimal}")


# 练习5: 变量交换
# Exercise 5: Variable Swapping
def exercise_5():
    """
    使用不同方法交换两个变量的值
    """
    print("\n=== 练习5: 变量交换 ===")
    
    # 方法1: 使用临时变量
    a, b = 10, 20
    print(f"交换前: a={a}, b={b}")
    
    # TODO: 使用临时变量交换
    temp = a
    a = b
    b = temp
    print(f"方法1 - 交换后: a={a}, b={b}")
    
    # 方法2: 使用元组
    a, b = 30, 40
    print(f"交换前: a={a}, b={b}")
    
    # TODO: 使用元组交换
    a, b = b, a
    print(f"方法2 - 交换后: a={a}, b={b}")


# 练习6: 用户输入处理
# Exercise 6: User Input Processing
def exercise_6():
    """
    创建一个简单的用户信息收集程序
    """
    print("\n=== 练习6: 用户输入处理 ===")
    
    # TODO: 收集用户信息并进行适当的类型转换
    # 注意：在实际运行时取消注释以下代码
    
    # name = input("请输入你的姓名: ")
    # age_str = input("请输入你的年龄: ")
    # height_str = input("请输入你的身高(米): ")
    
    # try:
    #     age = int(age_str)
    #     height = float(height_str)
    #     
    #     print(f"\n用户信息:")
    #     print(f"姓名: {name} (类型: {type(name).__name__})")
    #     print(f"年龄: {age} (类型: {type(age).__name__})")
    #     print(f"身高: {height} (类型: {type(height).__name__})")
    # except ValueError as e:
    #     print(f"输入错误: {e}")
    
    print("用户输入练习（需要取消注释代码才能运行）")


# 练习7: 真值测试
# Exercise 7: Truth Value Testing
def exercise_7():
    """
    测试不同值的真值
    """
    print("\n=== 练习7: 真值测试 ===")
    
    test_values = [
        0, 1, -1,           # 数字
        "", "hello",        # 字符串
        [], [1, 2, 3],      # 列表
        {}, {"key": "val"}, # 字典
        None, True, False   # 特殊值
    ]
    
    for value in test_values:
        truth_value = bool(value)
        print(f"{repr(value):15} -> {truth_value}")


# 挑战练习: BMI计算器
# Challenge Exercise: BMI Calculator
def challenge_bmi_calculator():
    """
    创建一个BMI（身体质量指数）计算器
    BMI = 体重(kg) / 身高(m)²
    """
    print("\n=== 挑战练习: BMI计算器 ===")
    
    # TODO: 实现BMI计算器
    # 提示：需要处理用户输入、类型转换、计算和结果分类
    
    def calculate_bmi(weight, height):
        """计算BMI值"""
        return weight / (height ** 2)
    
    def classify_bmi(bmi):
        """根据BMI值分类"""
        if bmi < 18.5:
            return "体重过轻"
        elif bmi < 24:
            return "正常体重"
        elif bmi < 28:
            return "超重"
        else:
            return "肥胖"
    
    # 示例计算
    weight = 70  # kg
    height = 1.75  # m
    
    bmi = calculate_bmi(weight, height)
    category = classify_bmi(bmi)
    
    print(f"体重: {weight}kg, 身高: {height}m")
    print(f"BMI: {bmi:.2f}")
    print(f"分类: {category}")


def main():
    """运行所有练习"""
    print("Python 变量和数据类型练习")
    print("=" * 50)
    
    exercise_1()
    exercise_2()
    exercise_3()
    exercise_4()
    exercise_5()
    exercise_6()
    exercise_7()
    challenge_bmi_calculator()


if __name__ == "__main__":
    main()
