# 4.3 Handling Binary Files with `struct` (使用`struct`处理二进制文件)

The `struct` module performs conversions between Python values and C structs represented as Python `bytes` objects.

`struct` 模块在Python值和表示为Python `bytes` 对象的C结构体之间执行转换。

## Packing and Unpacking Data (打包和解包数据)

*   `struct.pack(format, v1, v2, ...)`: Return a `bytes` object containing the values `v1, v2, ...` packed according to the given format.
*   `struct.unpack(format, buffer)`: Unpack from the buffer `buffer` (presumably packed by `pack(format, ...)`). The result is a tuple even if it contains exactly one item.

```python
# code/struct_examples.py

import struct

# Pack some values into a bytes object
# (将一些值打包成一个字节对象)
# The format string '>I2sH' means:
# >: big-endian
# I: unsigned int (4 bytes)
# 2s: 2-byte string
# H: unsigned short (2 bytes)
packed_data = struct.pack('>I2sH', 1, b'ab', 3)
print(f"Packed data: {packed_data}")

# Unpack the bytes object back into a tuple of values
# (将字节对象解包回一个值的元组)
unpacked_data = struct.unpack('>I2sH', packed_data)
print(f"Unpacked data: {unpacked_data}")
```

This is useful for working with binary file formats, such as images, sound files, and network packets.

这对于处理二进制文件格式（如图像、声音文件和网络数据包）非常有用。
