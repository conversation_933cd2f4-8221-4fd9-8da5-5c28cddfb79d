# 5. Operator Overloading (运算符重载)

Operator overloading allows you to define the behavior of operators for objects of your custom classes. For example, you can overload the `+` operator to concatenate two custom objects.

运算符重载允许你为自定义类的对象定义运算符的行为。例如，你可以重载 `+` 运算符，使其可以用于连接两个自定义对象。

## Example (示例)

Here is an example of a `Vector` class that overloads the `+` and `*` operators:

下面是一个 `Vector` 类的例子，它重载了 `+` 和 `*` 运算符：

```python
import math

class Vector:

    def __init__(self, x=0, y=0):
        self.x = x
        self.y = y

    def __repr__(self):
        return f'Vector({self.x!r}, {self.y!r})'

    def __abs__(self):
        return math.hypot(self.x, self.y)

    def __bool__(self):
        return bool(abs(self))

    def __add__(self, other):
        x = self.x + other.x
        y = self.y + other.y
        return Vector(x, y)

    def __mul__(self, scalar):
        return Vector(self.x * scalar, self.y * scalar)
```

Now you can use the `+` and `*` operators like this:

现在你可以像这样使用 `+` 和 `*` 运算符了：

```python
v1 = Vector(2, 4)
v2 = Vector(2, 1)
print(v1 + v2)  # Vector(4, 5)

v = Vector(3, 4)
print(v * 3)    # Vector(9, 12)

print(abs(v))   # 5.0
```

## Other Overloadable Operators (其他可重载的运算符)

Here are some other common operators you can overload:

以下是一些你可以重载的其他常见运算符：

*   `__sub__(self, other)`: `-`
*   `__truediv__(self, other)`: `/`
*   `__floordiv__(self, other)`: `//`
*   `__mod__(self, other)`: `%`
*   `__pow__(self, other)`: `**`
*   `__eq__(self, other)`: `==`
*   `__ne__(self, other)`: `!=`
*   `__lt__(self, other)`: `<`
*   `__le__(self, other)`: `<=`
*   `__gt__(self, other)`: `>`
*   `__ge__(self, other)`: `>=`