# obj_ref_examples.py

# Object References (对象引用)
print("--- Object References ---")
a = [1, 2, 3]
b = a
print(f"a = {a}, b = {b}")
print(f"a is b: {a is b}")
b.append(4)
print(f"After b.append(4), a = {a}, b = {b}")

# Mutability (可变性)
print("\n--- Mutability ---")
my_list = [1, 2, 3]
print(f"Original list: {my_list}")
my_list[0] = 100
print(f"Modified list: {my_list}")

my_tuple = (1, 2, 3)
print(f"Original tuple: {my_tuple}")
try:
    my_tuple[0] = 100
except TypeError as e:
    print(f"Error trying to modify a tuple: {e}")

# Garbage Collection (垃圾回收)
print("\n--- Garbage Collection ---")
import sys
a = [1, 2, 3]
print(f"Reference count of a: {sys.getrefcount(a)}")
b = a
print(f"Reference count of a after b = a: {sys.getrefcount(a)}")
del b
print(f"Reference count of a after del b: {sys.getrefcount(a)}")
