# 5.1 Basic Data Class (基本数据类)

The `@dataclass` decorator automatically generates several special methods for you, making your classes more concise.

`@dataclass` 装饰器会自动为你生成几个特殊方法，使你的类更加简洁。

## A Simple Data Class (一个简单的数据类)

Here is a simple example of a data class:

下面是一个简单的数据类示例：

```python
# code/basic_dataclass.py

from dataclasses import dataclass

@dataclass
class Card:
    rank: str
    suit: str

queen_of_hearts = Card('Q', 'hearts')
print(queen_of_hearts)
```

The `@dataclass` decorator automatically adds the following methods to the `Card` class:

`@dataclass` 装饰器会自动向 `Card` 类添加以下方法：

*   `__init__`: To initialize the object.
*   `__repr__`: To provide a developer-friendly string representation.
*   `__eq__`: To compare two objects for equality.

This is equivalent to writing:

这相当于编写：

```python
class Card:
    def __init__(self, rank: str, suit: str) -> None:
        self.rank = rank
        self.suit = suit

    def __repr__(self) -> str:
        return f"Card(rank={self.rank!r}, suit={self.suit!r})"

    def __eq__(self, other) -> bool:
        if not isinstance(other, Card):
            return NotImplemented
        return self.rank == other.rank and self.suit == other.suit
```

As you can see, the data class version is much more concise.

如你所见，数据类版本要简洁得多。
