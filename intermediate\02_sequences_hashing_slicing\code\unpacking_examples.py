# code/unpacking_examples.py

# Unpacking a tuple (解包一个元组)
lax_coordinates = (33.9425, -118.408056)
latitude, longitude = lax_coordinates  # unpacking

print(f"Latitude: {latitude}")
print(f"Longitude: {longitude}")

# Unpacking with a dummy variable (使用虚拟变量解包)
_, filename = ('/home/<USER>/data.csv'.split('/'))[-1], ('/home/<USER>/data.csv'.split('/'))[-1]

# Using * to grab excess items (使用*来获取多余的项目)
a, b, *rest = range(5)
print(f"a={a}, b={b}, rest={rest}")

# Nested unpacking (嵌套解包)
metro_areas = [
    ('Tokyo', 'JP', 36.933, (35.689722, 139.691667)),
    ('Delhi NCR', 'IN', 21.935, (28.613889, 77.208889)),
]

print("\n--- Nested Unpacking ---")
for name, _, _, (lat, lon) in metro_areas:
    print(f"{name}: lat={lat}, lon={lon}")
