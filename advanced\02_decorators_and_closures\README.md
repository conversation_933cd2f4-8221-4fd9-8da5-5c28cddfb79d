# Chapter 2: Decorators and Closures (装饰器与闭包)

This chapter explores decorators and closures, two powerful features in Python that are enabled by functions being first-class objects. We will learn how to create and use decorators, and understand the concept of closures and how they relate to decorators.

本章探讨了装饰器和闭包，这是Python中由函数作为一等公民所支持的两个强大功能。我们将学习如何创建和使用装饰器，并理解闭包的概念以及它们与装饰器的关系。

## Table of Contents (目录)

1.  [Decorators 101 (装饰器入门)](01_decorators_101.md)
2.  [Variable Scoping and Closures (变量作用域和闭包)](02_variable_scoping_and_closures.md)
3.  [Decorators with Parameters (带参数的装饰器)](03_decorators_with_parameters.md)
4.  [Stacked Decorators (堆叠装饰器)](04_stacked_decorators.md)
5.  [Standard Library Decorators (标准库中的装饰器)](05_standard_library_decorators.md)
