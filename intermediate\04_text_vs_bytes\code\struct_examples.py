# code/struct_examples.py

import struct

# Pack some values into a bytes object
# (将一些值打包成一个字节对象)
# The format string '>I2sH' means:
# >: big-endian
# I: unsigned int (4 bytes)
# 2s: 2-byte string
# H: unsigned short (2 bytes)
packed_data = struct.pack('>I2sH', 1, b'ab', 3)
print(f"Packed data: {packed_data}")

# Unpack the bytes object back into a tuple of values
# (将字节对象解包回一个值的元组)
unpacked_data = struct.unpack('>I2sH', packed_data)
print(f"Unpacked data: {unpacked_data}")
