# 2.3 Decorators with Parameters (带参数的装饰器)

Sometimes it's useful to have a decorator that takes arguments. This can be achieved by creating a decorator factory that returns a decorator.

有时，拥有一个接受参数的装饰器是很有用的。这可以通过创建一个返回装饰器的装饰器工厂来实现。

## A Decorator Factory (一个装饰器工厂)

Here is an example of a decorator factory that creates a decorator to repeat a function a certain number of times.

下面是一个装饰器工厂的例子，它创建一个装饰器来将一个函数重复一定次数。

```python
# code/decorator_with_parameters.py

import functools

def repeat(times):
    def decorator(func):
        @functools.wraps(func)
        def wrapper(*args, **kwargs):
            for _ in range(times):
                result = func(*args, **kwargs)
            return result
        return wrapper
    return decorator

@repeat(3)
def say_hello(name):
    print(f"Hello, {name}!")

say_hello("World")
```

In this example, `repeat(3)` is the decorator factory, and it returns the actual decorator that is applied to the `say_hello` function.

在这个例子中，`repeat(3)` 是装饰器工厂，它返回应用于 `say_hello` 函数的实际装饰器。
