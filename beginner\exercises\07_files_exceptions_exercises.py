#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
文件操作与异常处理练习题
File Operations and Exception Handling Exercises

完成以下练习来巩固你对Python文件操作和异常处理的理解。
Complete the following exercises to reinforce your understanding of Python file operations and exception handling.
"""

import os
import datetime
import json

# 练习1: 基本文件读写
# Exercise 1: Basic File Reading and Writing
def exercise_1():
    """
    创建一个文本文件，写入一些内容，然后读取并显示
    """
    print("=== 练习1: 基本文件读写 ===")
    
    filename = "test_file.txt"
    content = """这是第一行
这是第二行
这是第三行
Hello, World!
Python文件操作练习"""
    
    # TODO: 写入文件
    try:
        with open(filename, 'w', encoding='utf-8') as file:
            file.write(content)
        print(f"成功写入文件: {filename}")
        
        # TODO: 读取文件
        with open(filename, 'r', encoding='utf-8') as file:
            read_content = file.read()
        
        print("文件内容:")
        print(read_content)
        
    except Exception as e:
        print(f"文件操作出错: {e}")
    finally:
        # 清理：删除测试文件
        if os.path.exists(filename):
            os.remove(filename)
            print(f"已删除测试文件: {filename}")


# 练习2: 逐行处理文件
# Exercise 2: Line-by-Line File Processing
def exercise_2():
    """
    创建一个包含数字的文件，逐行读取并计算总和
    """
    print("\n=== 练习2: 逐行处理文件 ===")
    
    filename = "numbers.txt"
    numbers = [1, 2, 3, 4, 5, 10, 20, 30]
    
    try:
        # TODO: 写入数字到文件（每行一个数字）
        with open(filename, 'w', encoding='utf-8') as file:
            for num in numbers:
                file.write(f"{num}\n")
        
        # TODO: 逐行读取并计算总和
        total = 0
        count = 0
        
        with open(filename, 'r', encoding='utf-8') as file:
            for line_number, line in enumerate(file, 1):
                try:
                    number = int(line.strip())
                    total += number
                    count += 1
                    print(f"第{line_number}行: {number}")
                except ValueError:
                    print(f"第{line_number}行不是有效数字: {line.strip()}")
        
        print(f"总共读取了 {count} 个数字")
        print(f"总和: {total}")
        print(f"平均值: {total/count if count > 0 else 0:.2f}")
        
    except Exception as e:
        print(f"处理文件时出错: {e}")
    finally:
        if os.path.exists(filename):
            os.remove(filename)


# 练习3: 异常处理
# Exercise 3: Exception Handling
def exercise_3():
    """
    演示各种文件操作异常的处理
    """
    print("\n=== 练习3: 异常处理 ===")
    
    # 测试不同的异常情况
    test_cases = [
        ("nonexistent_file.txt", "尝试读取不存在的文件"),
        ("", "尝试使用空文件名"),
        ("/root/test.txt", "尝试访问无权限的路径"),
    ]
    
    for filename, description in test_cases:
        print(f"\n{description}: {filename}")
        try:
            with open(filename, 'r', encoding='utf-8') as file:
                content = file.read()
                print(f"成功读取: {content[:50]}...")
        except FileNotFoundError:
            print("错误: 文件不存在")
        except PermissionError:
            print("错误: 没有权限访问文件")
        except OSError as e:
            print(f"操作系统错误: {e}")
        except Exception as e:
            print(f"未知错误: {e}")


# 练习4: 安全的用户输入处理
# Exercise 4: Safe User Input Processing
def exercise_4():
    """
    创建一个安全的用户输入处理函数
    """
    print("\n=== 练习4: 安全的用户输入处理 ===")
    
    def safe_number_input(prompt, default=0):
        """安全地获取数字输入"""
        while True:
            try:
                user_input = input(prompt)
                if not user_input.strip():
                    return default
                return float(user_input)
            except ValueError:
                print("请输入有效的数字！")
            except KeyboardInterrupt:
                print("\n用户取消输入")
                return default
    
    def safe_file_input(prompt):
        """安全地获取文件名输入"""
        while True:
            try:
                filename = input(prompt).strip()
                if not filename:
                    print("文件名不能为空！")
                    continue
                
                # 检查文件是否存在
                if os.path.exists(filename):
                    return filename
                else:
                    create = input(f"文件 {filename} 不存在，是否创建？(y/n): ")
                    if create.lower() == 'y':
                        # 创建空文件
                        with open(filename, 'w', encoding='utf-8') as file:
                            file.write("")
                        print(f"已创建文件: {filename}")
                        return filename
                    else:
                        print("请输入其他文件名")
            except KeyboardInterrupt:
                print("\n用户取消输入")
                return None
    
    # 演示安全输入（注释掉以避免在自动测试时等待输入）
    # print("演示安全数字输入:")
    # number = safe_number_input("请输入一个数字（直接回车使用默认值0）: ")
    # print(f"你输入的数字是: {number}")
    
    print("安全输入函数已定义（取消注释可测试交互功能）")


# 练习5: 日志系统
# Exercise 5: Logging System
def exercise_5():
    """
    实现一个简单的日志系统
    """
    print("\n=== 练习5: 日志系统 ===")
    
    class Logger:
        def __init__(self, filename="app.log", max_size=1024):
            self.filename = filename
            self.max_size = max_size
        
        def _rotate_log(self):
            """日志轮转"""
            if os.path.exists(self.filename):
                if os.path.getsize(self.filename) > self.max_size:
                    backup_name = f"{self.filename}.old"
                    if os.path.exists(backup_name):
                        os.remove(backup_name)
                    os.rename(self.filename, backup_name)
        
        def log(self, level, message):
            """写入日志"""
            try:
                self._rotate_log()
                timestamp = datetime.datetime.now().strftime('%Y-%m-%d %H:%M:%S')
                log_entry = f"[{timestamp}] {level}: {message}\n"
                
                with open(self.filename, 'a', encoding='utf-8') as file:
                    file.write(log_entry)
                    
            except Exception as e:
                print(f"日志写入失败: {e}")
        
        def info(self, message):
            self.log("INFO", message)
        
        def warning(self, message):
            self.log("WARNING", message)
        
        def error(self, message):
            self.log("ERROR", message)
        
        def read_logs(self):
            """读取日志"""
            try:
                with open(self.filename, 'r', encoding='utf-8') as file:
                    return file.read()
            except FileNotFoundError:
                return "日志文件不存在"
            except Exception as e:
                return f"读取日志失败: {e}"
    
    # 测试日志系统
    logger = Logger("test.log", max_size=200)  # 小尺寸便于测试轮转
    
    logger.info("应用程序启动")
    logger.warning("这是一个警告消息")
    logger.error("这是一个错误消息")
    logger.info("处理用户请求")
    logger.info("这是一条很长的日志消息，用于测试日志轮转功能是否正常工作")
    
    print("日志内容:")
    print(logger.read_logs())
    
    # 清理测试文件
    for filename in ["test.log", "test.log.old"]:
        if os.path.exists(filename):
            os.remove(filename)


# 练习6: 配置文件处理
# Exercise 6: Configuration File Handling
def exercise_6():
    """
    创建和读取配置文件
    """
    print("\n=== 练习6: 配置文件处理 ===")
    
    def create_config_file(filename="config.ini"):
        """创建配置文件"""
        config_content = """# 应用程序配置文件
# Application Configuration File

[database]
host=localhost
port=5432
username=admin
password=secret123

[server]
host=0.0.0.0
port=8080
debug=True

[logging]
level=INFO
file=app.log
"""
        try:
            with open(filename, 'w', encoding='utf-8') as file:
                file.write(config_content)
            print(f"配置文件已创建: {filename}")
        except Exception as e:
            print(f"创建配置文件失败: {e}")
    
    def read_config_file(filename="config.ini"):
        """读取配置文件"""
        config = {}
        current_section = None
        
        try:
            with open(filename, 'r', encoding='utf-8') as file:
                for line_number, line in enumerate(file, 1):
                    line = line.strip()
                    
                    # 跳过空行和注释
                    if not line or line.startswith('#'):
                        continue
                    
                    # 处理节（section）
                    if line.startswith('[') and line.endswith(']'):
                        current_section = line[1:-1]
                        config[current_section] = {}
                        continue
                    
                    # 处理键值对
                    if '=' in line and current_section:
                        key, value = line.split('=', 1)
                        config[current_section][key.strip()] = value.strip()
                    else:
                        print(f"警告: 第{line_number}行格式不正确: {line}")
            
            return config
            
        except FileNotFoundError:
            print(f"配置文件 {filename} 不存在")
            return None
        except Exception as e:
            print(f"读取配置文件失败: {e}")
            return None
    
    # 测试配置文件处理
    config_file = "test_config.ini"
    
    create_config_file(config_file)
    config = read_config_file(config_file)
    
    if config:
        print("\n配置信息:")
        for section, settings in config.items():
            print(f"[{section}]")
            for key, value in settings.items():
                print(f"  {key} = {value}")
            print()
    
    # 清理测试文件
    if os.path.exists(config_file):
        os.remove(config_file)


# 挑战练习: 文件备份工具
# Challenge Exercise: File Backup Tool
def challenge_backup_tool():
    """
    创建一个简单的文件备份工具
    """
    print("\n=== 挑战练习: 文件备份工具 ===")
    
    import shutil
    import hashlib
    
    class BackupTool:
        def __init__(self, backup_dir="backups"):
            self.backup_dir = backup_dir
            self._ensure_backup_dir()
        
        def _ensure_backup_dir(self):
            """确保备份目录存在"""
            if not os.path.exists(self.backup_dir):
                os.makedirs(self.backup_dir)
        
        def _get_file_hash(self, filename):
            """计算文件的MD5哈希值"""
            try:
                with open(filename, 'rb') as file:
                    return hashlib.md5(file.read()).hexdigest()
            except Exception:
                return None
        
        def backup_file(self, source_file):
            """备份单个文件"""
            try:
                if not os.path.exists(source_file):
                    print(f"源文件不存在: {source_file}")
                    return False
                
                # 生成备份文件名
                timestamp = datetime.datetime.now().strftime('%Y%m%d_%H%M%S')
                filename = os.path.basename(source_file)
                backup_filename = f"{timestamp}_{filename}"
                backup_path = os.path.join(self.backup_dir, backup_filename)
                
                # 复制文件
                shutil.copy2(source_file, backup_path)
                
                # 验证备份
                source_hash = self._get_file_hash(source_file)
                backup_hash = self._get_file_hash(backup_path)
                
                if source_hash == backup_hash:
                    print(f"备份成功: {source_file} -> {backup_path}")
                    return True
                else:
                    print(f"备份验证失败: {source_file}")
                    os.remove(backup_path)
                    return False
                    
            except Exception as e:
                print(f"备份失败: {e}")
                return False
        
        def list_backups(self):
            """列出所有备份文件"""
            try:
                backups = []
                for filename in os.listdir(self.backup_dir):
                    filepath = os.path.join(self.backup_dir, filename)
                    if os.path.isfile(filepath):
                        stat = os.stat(filepath)
                        backups.append({
                            'filename': filename,
                            'size': stat.st_size,
                            'modified': datetime.datetime.fromtimestamp(stat.st_mtime)
                        })
                
                return sorted(backups, key=lambda x: x['modified'], reverse=True)
                
            except Exception as e:
                print(f"列出备份失败: {e}")
                return []
    
    # 测试备份工具
    backup_tool = BackupTool("test_backups")
    
    # 创建测试文件
    test_file = "test_backup_source.txt"
    with open(test_file, 'w', encoding='utf-8') as file:
        file.write("这是一个测试文件，用于备份工具测试。\n")
        file.write(f"创建时间: {datetime.datetime.now()}\n")
    
    # 执行备份
    backup_tool.backup_file(test_file)
    
    # 列出备份
    backups = backup_tool.list_backups()
    print("\n备份列表:")
    for backup in backups:
        print(f"  {backup['filename']} - {backup['size']} bytes - {backup['modified']}")
    
    # 清理测试文件
    if os.path.exists(test_file):
        os.remove(test_file)
    
    # 清理备份目录
    if os.path.exists("test_backups"):
        shutil.rmtree("test_backups")


def main():
    """运行所有练习"""
    print("Python 文件操作与异常处理练习")
    print("=" * 50)
    
    exercise_1()
    exercise_2()
    exercise_3()
    exercise_4()
    exercise_5()
    exercise_6()
    challenge_backup_tool()


if __name__ == "__main__":
    main()
