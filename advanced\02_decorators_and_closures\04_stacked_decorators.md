# 2.4 Stacked Decorators (堆叠装饰器)

You can apply multiple decorators to a single function by stacking them on top of each other.

你可以通过将多个装饰器堆叠在一起来将它们应用于单个函数。

```python
# code/stacked_decorators.py

def bold(func):
    def wrapper():
        return "<b>" + func() + "</b>"
    return wrapper

def italic(func):
    def wrapper():
        return "<i>" + func() + "</i>"
    return wrapper

@bold
@italic
def greet():
    return "Hello, World!"

print(greet())
```

The decorators are applied from the bottom up. In this example, `italic` is applied first, and then `bold` is applied to the result of `italic`.

装饰器从下到上应用。在这个例子中，首先应用 `italic`，然后将 `bold` 应用于 `italic` 的结果。
