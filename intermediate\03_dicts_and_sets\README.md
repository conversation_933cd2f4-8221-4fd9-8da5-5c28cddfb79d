# Chapter 3: Dictionaries and Sets (字典与集合)

This chapter covers dictionaries and sets, two of Python's most powerful and versatile data structures. We will explore their common methods, how to handle missing keys, and variations in the `collections` module.

本章涵盖了字典和集合，这是Python中两种最强大和通用的数据结构。我们将探讨它们的常用方法、如何处理缺失的键，以及`collections`模块中的变体。

## Table of Contents (目录)

1.  [Common Dictionary Methods (常用字典方法)](01_common_dictionary_methods.md)
2.  [Handling Missing Keys (处理缺失的键)](02_handling_missing_keys.md)
3.  [Dictionary Variants in `collections` (`collections`中的字典变体)](03_dictionary_variants.md)
4.  [Set Theory (集合论)](04_set_theory.md)
