# 1.2 A Pythonic Card Deck (一个Pythonic的扑克牌)

Let's start with a simple example: a class to represent a deck of playing cards.

让我们从一个简单的例子开始：一个表示一副扑克牌的类。

```python
# code/frenchdeck.py

import collections

Card = collections.namedtuple('Card', ['rank', 'suit'])

class FrenchDeck:
    ranks = [str(n) for n in range(2, 11)] + list('JQKA')
    suits = 'spades diamonds clubs hearts'.split()

    def __init__(self):
        self._cards = [Card(rank, suit) for suit in self.suits
                                        for rank in self.ranks]

    def __len__(self):
        return len(self._cards)

    def __getitem__(self, position):
        return self._cards[position]
```

By implementing the `__len__` and `__getitem__` special methods, our `FrenchDeck` class behaves like a standard Python sequence. We can get its length, retrieve a specific card, and even slice it.

通过实现 `__len__` 和 `__getitem__` 特殊方法，我们的 `FrenchDeck` 类的行为就像一个标准的Python序列。我们可以获取它的长度，检索一张特定的牌，甚至对它进行切片。

```python
>>> from frenchdeck import FrenchDeck, Card
>>> deck = FrenchDeck()
>>> len(deck)
52
>>> deck[0]
Card(rank='2', suit='spades')
>>> deck[-1]
Card(rank='A', suit='hearts')
```

Because `__getitem__` delegates to the `[]` operator of `self._cards`, our deck also supports slicing:

因为 `__getitem__` 委托给了 `self._cards` 的 `[]` 运算符，所以我们的牌堆也支持切片：

```python
>>> deck[:3]
[Card(rank='2', suit='spades'), Card(rank='3', suit='spades'), Card(rank='4', suit='spades')]
```

And it's iterable:

而且它是可迭代的：

```python
>>> for card in deck:
...     print(card)
```

This is the power of the data model. We didn't have to write a lot of code to get all this functionality. We just had to implement two special methods, and Python did the rest.

这就是数据模型的力量。我们不必编写大量代码来获得所有这些功能。我们只需要实现两个特殊方法，Python就会完成剩下的工作。
